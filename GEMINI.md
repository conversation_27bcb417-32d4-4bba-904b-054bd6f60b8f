# Contexto Específico do auth-service

Este documento fornece um contexto detalhado sobre o `auth-service`, seu estado atual, funcionalidades implementadas e as principais áreas que precisam ser alinhadas com as diretrizes da Quanthea Corp e o `TODO.md` do projeto.

## 1. Papel e Responsabilidades Atuais

O `auth-service` é a **autoridade de identidade central** no ecossistema da Quanthea Corp. Suas responsabilidades atuais incluem:

*   **Autenticação de Usuários:** Gerencia o registro, login (via email/senha), recuperação e alteração de senhas para usuários finais.
*   **Autenticação de Administradores:** Possui um fluxo de login e autenticação separado para usuários administradores.
*   **Emissão de JWTs:** Gera JSON Web Tokens para usuários e administradores, contendo claims como `user_id`, `email` e `tenant_id`.
*   **Gerenciamento de Tenants:** Armazena informações sobre tenants e seus hashes de API Key.
*   **Validação de API Keys:** Valida API Keys recebidas no cabeçalho `X-API-Key` para identificar o tenant da requisição.
*   **Segurança:** Implementa hashing de senhas (Argon2), proteção contra ataques de tempo, e middleware de segurança de cabeçalhos HTTP.
*   **Logging:** Possui um sistema de logging estruturado com Loguru, incluindo um logger dedicado para eventos de segurança.
*   **Multi-tenancy:** Garante o isolamento de dados de usuários por tenant.

## 2. Tecnologias Utilizadas (Implementação Atual)

*   **Linguagem:** Python
*   **Framework Web:** FastAPI
*   **ORM:** SQLAlchemy
*   **Banco de Dados:** PostgreSQL (configurado via `DATABASE_URL`, com compatibilidade para SQLite em `base.py`).
*   **Hashing de Senhas:** Argon2
*   **Autenticação:** JWT (JSON Web Tokens)
*   **Middleware:** CORS, Security Headers, Timing Attack Protection, Simple Rate Limit (in-memory), API Key Validation.
*   **Logging:** Loguru
*   **Email:** `smtplib` e Jinja2 para templates de email.

## 3. Modelos de Dados Principais

*   `Tenant`: Representa uma aplicação cliente, com `id`, `name`, `description`, `is_active` e `api_key_hash`.
*   `User`: Usuários finais, associados a um `tenant_id`, com `email`, `hashed_password`, `is_active`, `reset_token` e `reset_token_expires`.
*   `AdminUser`: Usuários administrativos, com `email`, `hashed_password` e `is_active`.

## 4. Principais Endpoints Implementados

*   `/api/v1/auth/register` (POST)
*   `/api/v1/auth/login` (POST)
*   `/api/v1/auth/me` (GET)
*   `/api/v1/auth/forgot-password` (POST)
*   `/api/v1/auth/reset-password` (POST)
*   `/api/v1/auth/change-password` (POST)
*   `/api/v1/admin/login` (POST)
*   `/api/v1/admin/tenants` (GET)
*   `/api/v1/admin/tenants/{tenant_id}/api-key` (POST/PUT/DELETE)
*   `/health` (GET)

## 5. Discrepâncias e Próximos Passos (Alinhamento com QUANTHEACORP.md e TODO.md)

As seguintes áreas são cruciais para alinhar o `auth-service` com as especificações da holding e as melhorias planejadas:

*   **Refatorar Validação de Chaves de API (Prioridade Alta):** A validação atual itera sobre todos os tenants no banco de dados (`APIKeyMiddleware`). **É fundamental implementar a abordagem "push-based" via Redis**, onde o `admin-service` publica hashes de chaves no Redis e o `auth-service` consome e cacheia essas informações localmente para validação rápida.
*   **Corrigir Variável `FRONTEND_URL` (Prioridade Alta):** A variável `settings.FRONTEND_URL` é usada em `app/core/email.py` mas não está definida em `app/core/config.py`.
*   **Implementar Rate Limiting Distribuído (Prioridade Média):** O `SimpleRateLimitMiddleware` atual é em memória e não funciona em ambientes distribuídos. Deve ser substituído por uma solução baseada em Redis ou via API Gateway.
*   **Implementar Métricas Abrangentes (`/admin/stats`) (Prioridade Média):** O serviço precisa expor métricas de uso e performance conforme o contrato `/admin/stats` do `QUANTHEACORP.md`.
*   **Gerenciamento de Segredos em Produção (Prioridade Média):** Atualmente, segredos são gerenciados via `.env`. Para produção, deve-se integrar com uma solução de gerenciamento de segredos dedicada (ex: HashiCorp Vault, AWS Secrets Manager).
*   **Refinar Consistência do Campo `username` (Prioridade Baixa):** Clarificar o propósito do campo `username` no modelo `User`, já que o login é feito por `email`.
*   **Gerenciamento de Dependências (Locking) (Prioridade Baixa):** Utilizar `pip-tools` para gerar um arquivo de dependências fixas (`requirements.lock`) para builds reprodutíveis.
*   **Otimização do Tamanho da Imagem Docker (Prioridade Baixa):** Avaliar a necessidade de ferramentas de cliente PostgreSQL na imagem final para reduzir seu tamanho.
*   **Revisar Logging - Consistência do `extra` (Prioridade Baixa):** Garantir que o `extra` seja sempre um dicionário e que o `JSONFormatter` o incorpore diretamente no log JSON sem aninhamento desnecessário.

## 6. Considerações Adicionais

*   **Testes:** A cobertura de testes precisa ser expandida significativamente, conforme diretriz geral do projeto.
*   **Docstrings:** As docstrings existentes são um bom começo, mas devem ser mantidas ricas e informativas para todas as funções e classes.
*   **Idioma:** O código e os nomes de arquivos devem seguir o padrão de inglês.
