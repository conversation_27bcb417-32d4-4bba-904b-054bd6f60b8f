# 🔐 Microserviço de Autenticação JWT

Microserviço seguro de autenticação com JWT, rate limiting, logging de segurança e headers de proteção.

## ✨ Funcionalidades

- 🔑 **Autenticação JWT** - Login seguro com tokens
- 📧 **Recuperação de senha** - Reset via email (SMTP Hostinger)
- 🛡️ **Rate Limiting** - Proteção contra ataques de força bruta
- 📊 **Logging de segurança** - Auditoria completa de eventos
- 🔒 **Headers de segurança** - Proteção contra ataques web
- 🌐 **CORS configurável** - Suporte para diferentes ambientes
- 📱 **Acesso via rede** - Funciona em dispositivos móveis

## 🏗️ Arquitetura

- **Backend:** FastAPI + PostgreSQL
- **Autenticação:** JWT com Argon2 para hash de senhas
- **Email:** SMTP Hostinger para recuperação de senha
- **Containerização:** Docker + Docker Compose
- **Logs:** Estruturados em JSON para produção

## Como subir o microserviço

1.  **Navegue até o diretório do serviço:**

    ```bash
    cd /home/<USER>/DevDocker/python/auth_service
    ```

2.  **Suba os contêineres com o Docker Compose:**

    ```bash
    docker compose up -d --build
    ```

    Este comando irá construir a imagem da aplicação, baixar a imagem do PostgreSQL e iniciar os dois contêineres em background.

3.  **Aplique as migrações do banco de dados:**
    ```bash
    docker compose exec app alembic upgrade head
    ```
    Este comando executa as migrações do Alembic dentro do contêiner da aplicação, criando a tabela de usuários no banco de dados.

Após esses passos, o microserviço estará rodando:

- **API:** `http://localhost:8002`
- **Swagger:** `http://localhost:8002/docs`
- **Rede local:** `http://SEU_IP:8002` (para dispositivos móveis)

## 🔗 Endpoints Disponíveis

| Endpoint                       | Método | Descrição                           |
| ------------------------------ | ------ | ----------------------------------- |
| `/api/v1/auth/register`        | POST   | Registro de usuário                 |
| `/api/v1/auth/login`           | POST   | Login (usa email no campo username) |
| `/api/v1/auth/forgot-password` | POST   | Solicitar reset de senha            |
| `/api/v1/auth/reset-password`  | POST   | Executar reset com token            |
| `/api/v1/auth/change-password` | POST   | Trocar senha (usuário logado)       |
| `/health`                      | GET    | Health check                        |

## 📚 Documentação

- **[📖 Guia de Integração](./INTEGRATION_GUIDE.md)** - Como integrar com frontends
- **[📋 TODO](./TODO.md)** - Tarefas e melhorias planejadas
- **[📁 Docs](./docs/)** - Documentação técnica adicional

## ⚙️ Configuração

### **Variáveis de Ambiente (.env):**

```bash
# Ambiente
ENVIRONMENT=development  # development, staging, production

# Database
DATABASE_URL=postgresql://user:pass@localhost:5434/auth_db

# JWT
SECRET_KEY=your-super-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# SMTP (Hostinger)
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-password

# Logging
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR
LOG_FILE_ENABLED=true

# Segurança
RATE_LIMIT_ENABLED=true
SECURITY_HEADERS_ENABLED=true
```

## 🔒 Segurança Implementada

- ✅ **Rate Limiting** - 5 tentativas de login por 15 minutos
- ✅ **Headers de Segurança** - CSP, X-Frame-Options, etc.
- ✅ **Logging de Auditoria** - Todos os eventos de segurança
- ✅ **Hash Argon2** - Senhas seguras
- ✅ **JWT com expiração** - Tokens temporários
- ✅ **Validação de entrada** - Proteção contra injeção

## 🚀 Deploy

### **Desenvolvimento:**

```bash
docker compose up -d --build
```

### **Produção:**

Consulte [docs/SUPABASE_MICROSERVICE_DEPLOY.md](./docs/SUPABASE_MICROSERVICE_DEPLOY.md) para deploy no Supabase.

## 🧪 Testes

```bash
# Executar testes
docker compose exec app pytest

# Teste manual via Swagger
http://localhost:8002/docs
```

## 📊 Monitoramento

- **Logs:** `logs/app.log` e `logs/security_audit.log`
- **Health Check:** `GET /health`
- **Métricas:** Rate limiting e eventos de segurança

---

**🔗 Links Úteis:**

- [Documentação Técnica](./docs/)
- [Guia de Integração](./INTEGRATION_GUIDE.md)
- [Lista de TODOs](./TODO.md)
