#!/bin/bash

# Entrypoint script for production deployment
# Applies database migrations and starts the application safely

set -e  # Exit immediately if a command exits with a non-zero status

echo "🚀 Starting Auth Microservice..."

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if database is accessible
check_database() {
    log "🔍 Checking database connectivity..."
    
    if [ -z "$DATABASE_URL" ]; then
        log "❌ ERROR: DATABASE_URL environment variable is not set"
        exit 1
    fi # Corrected: changed '}' to 'fi'
    
    # Try to connect to database using Python with async SQLAlchemy
    python -c "
import sys
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from app.core.config import settings

async def check_db_connection():
    try:
        engine = create_async_engine(settings.DATABASE_URL)
        async with engine.connect() as conn:
            await conn.execute(text('SELECT 1'))
        print('✅ Database connection successful')
        return 0
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
        return 1

if __name__ == '__main__':
    sys.exit(asyncio.run(check_db_connection()))
"
    
    if [ $? -ne 0 ]; then
        log "❌ Database connectivity check failed"
        exit 1
    fi
    
    log "✅ Database is accessible"
}

# Function to run database migrations
run_migrations() {
    log "📊 Running database migrations..."

    # Check if alembic is properly configured
    if [ ! -f "alembic.ini" ]; then
        log "❌ ERROR: alembic.ini not found"
        exit 1
    fi

    # Check current migration state
    log "🔍 Checking current migration state..."
    current_revision=$(alembic current 2>/dev/null | tail -n 1 | awk '{print $1}')

    if [ -z "$current_revision" ]; then
        log "📝 No migrations applied yet, starting from base"
    else
        log "📍 Current migration: $current_revision"
    fi

    # Show pending migrations
    log "📋 Checking for pending migrations..."
    alembic show head

    # Run migrations with retry logic
    max_retries=3
    retry_count=0

    while [ $retry_count -lt $max_retries ]; do
        log "🚀 Attempting migration (attempt $((retry_count + 1))/$max_retries)..."

        if alembic upgrade head; then
            log "✅ Database migrations completed successfully"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                log "⚠️ Migration failed, retrying in 5 seconds..."
                sleep 5
            else
                log "❌ Database migrations failed after $max_retries attempts"
                exit 1
            fi
        fi
    done
}

# Function to validate environment variables
validate_environment() {
    log "🔧 Validating environment variables..."
    
    # Required variables
    required_vars=("DATABASE_URL" "SECRET_KEY")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log "❌ ERROR: Required environment variable $var is not set"
            exit 1
        fi
    done
    
    # Check SECRET_KEY length (should be at least 32 characters)
    if [ ${#SECRET_KEY} -lt 32 ]; then
        log "⚠️  WARNING: SECRET_KEY should be at least 32 characters long"
    fi
    
    log "✅ Environment variables validated"
}

# Function to create necessary directories
setup_directories() {
    log "📁 Setting up directories..."
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Set proper permissions
    chmod 755 logs
    
    log "✅ Directories setup completed"
}

# Function to start the application
start_application() {
    log "🌟 Starting FastAPI application..."
    
    # Set production environment
    export ENVIRONMENT=production
    export PYTHONPATH=/app
    
    # Start uvicorn with production settings
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port ${PORT:-8000} \
        --workers 1 \
        --log-level info \
        --access-log \
        --no-use-colors \
        --loop uvloop \
        --http httptools
}

# Main execution flow
main() {
    log "🎯 Environment: ${ENVIRONMENT:-production}"
    log "🔧 Python version: $(python --version)"
    log "📍 Working directory: $(pwd)"
    
    # Step 1: Validate environment
    validate_environment
    
    # Step 2: Setup directories
    setup_directories
    
    # Step 3: Check database connectivity
    check_database
    
    # Step 4: Run migrations (safe - won't lose data)
    run_migrations
    
    # Step 5: Start application
    start_application
}

# Handle signals gracefully
trap 'log "🛑 Received shutdown signal, stopping..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
