#!/bin/bash

# Entrypoint script for production deployment
# Applies database migrations and starts the application safely

set -e  # Exit immediately if a command exits with a non-zero status

echo "Starting Quanthea Auth Service..."

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if database is accessible
check_database() {
    if [ -z "$DATABASE_URL" ]; then
        echo "ERROR: DATABASE_URL environment variable is not set"
        exit 1
    fi

    # Try to connect to database using Python with async SQLAlchemy
    python -c "
import sys
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
from app.core.config import settings

async def check_db_connection():
    try:
        engine = create_async_engine(settings.async_database_url)
        async with engine.connect() as conn:
            await conn.execute(text('SELECT 1'))
        return 0
    except Exception as e:
        print(f'Database connection failed: {e}')
        return 1

if __name__ == '__main__':
    sys.exit(asyncio.run(check_db_connection()))
" > /dev/null 2>&1

    if [ $? -ne 0 ]; then
        echo "Database connectivity check failed"
        exit 1
    fi

    log "Database is accessible"
}

# Function to run database migrations
run_migrations() {
    if [ ! -f "alembic.ini" ]; then
        echo "ERROR: alembic.ini not found"
        exit 1
    fi

    # Run migrations with retry logic
    max_retries=3
    retry_count=0

    while [ $retry_count -lt $max_retries ]; do
        if alembic upgrade head > /dev/null 2>&1; then
            log "Database migrations completed successfully"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                sleep 5
            else
                echo "Database migrations failed after $max_retries attempts"
                exit 1
            fi
        fi
    done
}

# Function to validate environment variables
validate_environment() {
    required_vars=("DATABASE_URL" "SECRET_KEY")

    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo "ERROR: Required environment variable $var is not set"
            exit 1
        fi
    done
}

# Function to create necessary directories
setup_directories() {
    mkdir -p logs
    chmod 755 logs
}

# Function to start the application
start_application() {
    log "Starting FastAPI application..."

    # Set production environment
    export ENVIRONMENT=production
    export PYTHONPATH=/app

    # Start uvicorn with production settings
    exec uvicorn app.main:app \
        --host 0.0.0.0 \
        --port ${PORT:-8000} \
        --workers 1 \
        --log-level info \
        --access-log \
        --no-use-colors \
        --loop uvloop \
        --http httptools
}

# Main execution flow
main() {
    log "Python version: $(python --version)"

    validate_environment
    setup_directories
    check_database
    run_migrations
    start_application
}

# Handle signals gracefully
trap 'echo "Received shutdown signal, stopping..."; exit 0' SIGTERM SIGINT

# Run main function
main "$@"
