"""Add allowed_frontend_origins to Tenant model

Revision ID: be0324dfe5ad
Revises: 0f03e03bd554
Create Date: 2025-07-13 16:37:35.995408

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'be0324dfe5ad'
down_revision: Union[str, Sequence[str], None] = '0f03e03bd554'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenants', sa.Column('allowed_frontend_origins', postgresql.ARRAY(sa.String()), server_default='{}', nullable=False, comment='List of trusted frontend origins for the tenant'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tenants', 'allowed_frontend_origins')
    # ### end Alembic commands ###
