"""Initial consolidated migration

Revision ID: 0f03e03bd554
Revises: 
Create Date: 2025-07-13 07:33:40.397395

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '0f03e03bd554'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admin_users',
    sa.Column('id', sa.UUID(), nullable=False, comment='Unique identifier for the admin user'),
    sa.Column('email', sa.String(length=255), nullable=False, comment="Admin user's email (unique globally)"),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment="Hash of the admin user's password"),
    sa.Column('is_active', sa.<PERSON>(), nullable=False, comment='Whether the admin user is active'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Record creation timestamp (UTC)'),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Last update timestamp (UTC)'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_admin_users_email'), 'admin_users', ['email'], unique=True)
    op.create_index(op.f('ix_admin_users_id'), 'admin_users', ['id'], unique=False)
    op.create_table('tenants',
    sa.Column('id', sa.UUID(), nullable=False, comment='Unique identifier for the tenant'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='Name of the tenant/application'),
    sa.Column('description', sa.Text(), nullable=True, comment='Description of the tenant/application'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether the tenant is active'),
    sa.Column('api_key_hash', sa.String(length=255), nullable=True, comment='Hashed API key for the tenant'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Record creation timestamp (UTC)'),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Last update timestamp (UTC)'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', name='uq_tenant_name')
    )
    op.create_index(op.f('ix_tenants_id'), 'tenants', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False, comment='Unique identifier for the user'),
    sa.Column('tenant_id', sa.UUID(), nullable=False, comment='ID of the tenant to which the user belongs'),
    sa.Column('email', sa.String(length=255), nullable=False, comment="User's email (unique per tenant)"),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment="Hash of the user's password"),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether the user is active'),
    sa.Column('reset_token', sa.String(length=255), nullable=True, comment='Token for password reset'),
    sa.Column('reset_token_expires', postgresql.TIMESTAMP(timezone=True), nullable=True, comment='Expiration of the reset token'),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Record creation timestamp (UTC)'),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Last update timestamp (UTC)'),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'email', name='uq_tenant_email')
    )
    op.create_index('idx_tenant_user_active', 'users', ['tenant_id', 'is_active'], unique=False)
    op.create_index('idx_user_email_lookup', 'users', ['tenant_id', 'email'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_tenant_id'), 'users', ['tenant_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_tenant_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index('idx_user_email_lookup', table_name='users')
    op.drop_index('idx_tenant_user_active', table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_tenants_id'), table_name='tenants')
    op.drop_table('tenants')
    op.drop_index(op.f('ix_admin_users_id'), table_name='admin_users')
    op.drop_index(op.f('ix_admin_users_email'), table_name='admin_users')
    op.drop_table('admin_users')
    # ### end Alembic commands ###
