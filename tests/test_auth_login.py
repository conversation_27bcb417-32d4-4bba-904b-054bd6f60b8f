import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from httpx import <PERSON>ync<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from uuid import UUID

from app.db.models import User
from app.core.security import get_password_hash


@pytest.mark.asyncio
async def test_login_success(async_client: AsyncClient, db: AsyncSession, tenant_headers, test_user):
    """Test successful user login with valid credentials."""
    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password123"},
        headers=tenant_headers
    )
    
    assert response.status_code == 200
    data = response.json()
    
    # Check response data
    assert data["token_type"] == "bearer"
    assert "access_token" in data
    assert data["tenant_id"] == str(test_user.tenant_id)
    assert data["tenant_name"] == "Test Tenant"


@pytest.mark.asyncio
async def test_login_invalid_credentials(async_client: AsyncClient, tenant_headers, test_user):
    """Test login fails with invalid password."""
    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "wrongpassword"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "Incorrect email or password" in response.json()["detail"]


@pytest.mark.asyncio
async def test_login_nonexistent_user(async_client: AsyncClient, tenant_headers):
    """Test login fails with nonexistent user."""
    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "password123"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "Incorrect email or password" in response.json()["detail"]


@pytest.mark.asyncio
async def test_login_inactive_user(async_client: AsyncClient, db: AsyncSession, tenant_headers, test_tenant):
    """Test login fails with inactive user."""
    # Create inactive user
    inactive_user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password123"),
        tenant_id=test_tenant.id,
        is_active=False
    )
    db.add(inactive_user)
    await db.commit()

    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": inactive_user.email, "password": "password123"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "Inactive user" in response.json()["detail"]


@pytest.mark.asyncio
async def test_login_without_api_key(async_client: AsyncClient, test_user):
    """Test login fails without API key."""
    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password123"}
    )

    assert response.status_code == 401
    assert "API key required" in response.json()["detail"]


@pytest.mark.asyncio
async def test_login_with_invalid_api_key(async_client: AsyncClient, test_user):
    """Test login fails with invalid API key."""
    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": test_user.email, "password": "password123"},
        headers={"X-API-Key": "invalid_key"}
    )

    assert response.status_code == 401
    assert "Invalid API key" in response.json()["detail"]