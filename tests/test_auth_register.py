import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from uuid import UUID

from app.main import app
from app.db.models import User


@pytest.mark.asyncio
async def test_register_success(async_client: AsyncClient, db: AsyncSession, tenant_headers):
    """Test successful user registration with valid data."""
    email = "<EMAIL>"
    password = "StrongPassword123!"

    response = await async_client.post(
        "/api/v1/auth/register",
        json={"email": email, "password": password},
        headers=tenant_headers
    )

    assert response.status_code == 200
    data = response.json()
    
    # Check response data
    assert data["email"] == email
    assert "id" in data
    assert "tenant_id" in data
    assert data["is_active"] is True
    
    # Verify user was created in database
    result = await db.execute(select(User).filter(User.email == email))
    user = result.scalar_one_or_none()
    assert user is not None
    assert user.email == email
    assert user.tenant_id == UUID(data["tenant_id"])


@pytest.mark.asyncio
async def test_register_duplicate_email(async_client: AsyncClient, db: AsyncSession, tenant_headers, test_user):
    """Test registration fails with already registered email."""

    response = await async_client.post(
        "/api/v1/auth/register",
        json={"email": test_user.email, "password": "AnotherPassword123!"},
        headers=tenant_headers
    )
    
    assert response.status_code == 400
    assert "already exists" in response.json()["detail"]


@pytest.mark.asyncio
async def test_register_invalid_email(async_client: AsyncClient, tenant_headers):
    """Test registration fails with invalid email format."""

    response = await async_client.post(
        "/api/v1/auth/register",
        json={"email": "invalid-email", "password": "Password123!"},
        headers=tenant_headers
    )
    
    assert response.status_code == 422
