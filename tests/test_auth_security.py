import pytest
from fastapi.testclient import Test<PERSON>lient
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession


@pytest.mark.asyncio
async def test_register_without_api_key(async_client: AsyncClient):
    """Test that register fails without API key."""

    response = await async_client.post(
        "/api/v1/auth/register",
        json={"email": "<EMAIL>", "password": "Password123!"}
    )
    
    assert response.status_code == 401
    assert "API key required" in response.json()["detail"]


@pytest.mark.asyncio
async def test_login_without_api_key(async_client: AsyncClient):
    """Test that login fails without API key."""

    response = await async_client.post(
        "/api/v1/auth/login",
        data={"username": "<EMAIL>", "password": "Password123!"}
    )

    assert response.status_code == 401
    assert "API key required" in response.json()["detail"]


@pytest.mark.asyncio
async def test_register_with_invalid_api_key(async_client: AsyncClient, db: AsyncSession):
    """Test that register fails with invalid API key."""

    response = await async_client.post(
        "/api/v1/auth/register",
        json={"email": "<EMAIL>", "password": "Password123!"},
        headers={"X-API-Key": "invalid_key"}
    )

    assert response.status_code == 401
    assert "Invalid API key" in response.json()["detail"]