import pytest
from fastapi import <PERSON>TT<PERSON>Ex<PERSON>
from fastapi.testclient import <PERSON><PERSON><PERSON>
from httpx import Async<PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from unittest.mock import Mock, AsyncMock

from app.api.v1 import deps
from app.db.models import User


@pytest.mark.asyncio
async def test_get_db():
    """Test database session dependency."""
    db_gen = deps.get_db()
    db = await db_gen.__anext__()
    
    # Should return a database session
    assert db is not None
    
    # Clean up
    try:
        await db_gen.__anext__()
    except StopAsyncIteration:
        pass  # Expected behavior


@pytest.mark.asyncio
async def test_get_current_user_success(db: AsyncSession, test_user, test_tenant):
    """Test successful user retrieval from valid token."""
    from app.core.security import create_access_token
    from datetime import timedelta    
    # Create valid token
    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "tenant_id": str(test_user.tenant_id)
    }
    
    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )
    
    # Mock request with tenant context
    mock_request = Mock()
    mock_request.state.tenant_id = str(test_user.tenant_id)
    
    # Test the function
    user = await deps.get_current_user(db=db, token=access_token, request=mock_request)
    
    assert user.id == test_user.id
    assert user.email == test_user.email
    assert user.tenant_id == test_user.tenant_id


@pytest.mark.asyncio
async def test_get_current_user_invalid_token(db: AsyncSession):
    """Test user retrieval fails with invalid token."""
    mock_request = Mock()
    mock_request.state.tenant_id = "some-tenant-id"
    
    with pytest.raises(HTTPException) as exc_info:
        await deps.get_current_user(db=db, token="invalid_token", request=mock_request)

    assert exc_info.value.status_code == 403
    assert "Could not validate credentials" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_current_user_user_not_found(db: AsyncSession, test_tenant):
    """Test user retrieval fails when user doesn't exist."""
    from app.core.security import create_access_token
    from datetime import timedelta    # Create token for non-existent user
    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": "99999999-9999-9999-9999-999999999999",
        "email": "<EMAIL>",
        "tenant_id": str(test_tenant.id)
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    mock_request = Mock()
    mock_request.state.tenant_id = str(test_tenant.id)

    with pytest.raises(HTTPException) as exc_info:
        await deps.get_current_user(db=db, token=access_token, request=mock_request)

    assert exc_info.value.status_code == 404
    assert "User not found" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_current_user_tenant_mismatch(db: AsyncSession, test_user):
    """Test user retrieval fails when tenant doesn't match."""
    from app.core.security import create_access_token
    from datetime import timedelta
    import uuid    # Create valid token
    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "tenant_id": str(test_user.tenant_id)
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    # Mock request with different tenant (valid UUID)
    mock_request = Mock()
    mock_request.state.tenant_id = str(uuid.uuid4())

    with pytest.raises(HTTPException) as exc_info:
        await deps.get_current_user(db=db, token=access_token, request=mock_request)

    assert exc_info.value.status_code == 403
    assert "Token tenant mismatch" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_get_tenant_user_by_email_success(db: AsyncSession, test_user, test_tenant):
    """Test successful user retrieval by email and tenant."""

    user = await deps.get_tenant_user_by_email(
        email=test_user.email,
        tenant_id=str(test_user.tenant_id),
        db=db
    )
    
    assert user is not None
    assert user.id == test_user.id
    assert user.email == test_user.email


@pytest.mark.asyncio
async def test_get_tenant_user_by_email_not_found(db: AsyncSession, test_tenant):
    """Test user retrieval returns None when user not found."""

    user = await deps.get_tenant_user_by_email(
        email="<EMAIL>",
        tenant_id=str(test_tenant.id),
        db=db
    )
    
    assert user is None


@pytest.mark.asyncio
async def test_get_tenant_user_by_email_wrong_tenant(db: AsyncSession, test_user):
    """Test user retrieval returns None when tenant doesn't match."""
    import uuid

    user = await deps.get_tenant_user_by_email(
        email=test_user.email,
        tenant_id=str(uuid.uuid4()),
        db=db
    )

    assert user is None
