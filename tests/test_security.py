import pytest
from datetime import datetime, timedelta, timezone
from jose import jwt

from app.core import security
from app.core.config import settings


def test_verify_password():
    """Test password verification."""
    password = "testpassword123"
    hashed = security.get_password_hash(password)
    
    # Correct password should verify
    assert security.verify_password(password, hashed) is True
    
    # Wrong password should not verify
    assert security.verify_password("wrongpassword", hashed) is False


def test_get_password_hash():
    """Test password hashing."""
    password = "testpassword123"
    hashed = security.get_password_hash(password)
    
    # Hash should be different from original password
    assert hashed != password
    
    # Hash should be consistent
    assert security.verify_password(password, hashed) is True


def test_create_access_token():
    """Test JWT access token creation."""
    data = {"sub": "<EMAIL>", "tenant_id": "123"}
    token = security.create_access_token(data=data)
    
    # Should return a string token
    assert isinstance(token, str)
    
    # Should be decodable
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
    assert payload["sub"] == "<EMAIL>"
    assert payload["tenant_id"] == "123"
    assert "exp" in payload


def test_create_access_token_with_expires_delta():
    """Test JWT token creation with custom expiration."""
    data = {"sub": "<EMAIL>"}
    expires_delta = timedelta(minutes=15)
    
    token = security.create_access_token(data=data, expires_delta=expires_delta)
    
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
    
    # Check expiration is approximately correct (within 1 minute tolerance)
    exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
    expected_exp = datetime.now(timezone.utc) + expires_delta
    
    assert abs((exp_time - expected_exp).total_seconds()) < 60


def test_create_password_reset_token():
    """Test password reset token creation."""
    email = "<EMAIL>"
    token = security.create_password_reset_token(email)
    
    # Should return a string token
    assert isinstance(token, str)
    
    # Should be decodable and contain email
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
    assert payload["sub"] == email
    assert "exp" in payload


def test_verify_password_reset_token_valid():
    """Test verification of valid password reset token."""
    email = "<EMAIL>"
    token = security.create_password_reset_token(email)
    
    # Should return the email
    verified_email = security.verify_password_reset_token(token)
    assert verified_email == email


def test_verify_password_reset_token_invalid():
    """Test verification of invalid password reset token."""
    # Invalid token should return None
    result = security.verify_password_reset_token("invalid_token")
    assert result is None


def test_verify_password_reset_token_expired():
    """Test verification of expired password reset token."""
    email = "<EMAIL>"
    
    # Create token with negative expiration (already expired)
    expired_token = security.create_password_reset_token(email)
    
    # Manually create an expired token
    from jose import jwt
    from datetime import datetime, timezone, timedelta
    
    payload = {
        "sub": email,
        "exp": datetime.now(timezone.utc) - timedelta(hours=1)  # Expired 1 hour ago
    }
    expired_token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    # Should return None for expired token
    result = security.verify_password_reset_token(expired_token)
    assert result is None


def test_create_access_token_subject_parameter():
    """Test JWT token creation with subject parameter (legacy support)."""
    subject = "<EMAIL>"
    token = security.create_access_token(subject=subject)
    
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
    assert payload["sub"] == subject


def test_password_hash_different_each_time():
    """Test that password hashing produces different hashes each time."""
    password = "testpassword123"
    hash1 = security.get_password_hash(password)
    hash2 = security.get_password_hash(password)
    
    # Hashes should be different (due to salt)
    assert hash1 != hash2
    
    # But both should verify correctly
    assert security.verify_password(password, hash1) is True
    assert security.verify_password(password, hash2) is True


def test_token_contains_required_fields():
    """Test that tokens contain required fields."""
    data = {"sub": "<EMAIL>"}
    token = security.create_access_token(data=data)

    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])

    # Check required fields
    assert "sub" in payload
    assert "exp" in payload
    assert payload["sub"] == "<EMAIL>"

    # exp should be in the future
    exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
    now = datetime.now(timezone.utc)
    assert exp_time > now
