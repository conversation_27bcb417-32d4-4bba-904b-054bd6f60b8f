"""Tests for Redis Service"""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from datetime import datetime, timezone

from app.core.redis import RedisService, get_redis_service


class TestRedisService:
    
    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client for testing"""
        mock_client = AsyncMock()
        mock_client.ping.return_value = True
        mock_client.set.return_value = True
        mock_client.get.return_value = None
        mock_client.delete.return_value = 1
        return mock_client
    
    @pytest.fixture
    def redis_service(self, mock_redis_client):
        """Redis service with mocked client"""
        service = RedisService()
        service._client = mock_redis_client
        return service
    
    def test_hash_api_key(self, redis_service):
        """Test API key hashing"""
        api_key = "test-api-key-123"
        hash1 = redis_service._hash_api_key(api_key)
        hash2 = redis_service._hash_api_key(api_key)
        
        assert hash1 == hash2
        assert len(hash1) == 64
        assert hash1 != api_key
    
    @pytest.mark.asyncio
    async def test_store_api_key_success(self, redis_service, mock_redis_client):
        """Test successful API key storage"""
        api_key_hash = "test_hash"
        tenant_id = "test_tenant_id"
        
        result = await redis_service.store_api_key(api_key_hash, tenant_id)
        
        assert result is True
        mock_redis_client.set.assert_awaited_once()
        
        call_args = mock_redis_client.set.call_args
        key = call_args[0][0]
        value = json.loads(call_args[0][1])
        
        assert key == f"api_key:{api_key_hash}"
        assert value["tenant_id"] == tenant_id
        assert value["revoked"] is False
        assert "created_at" in value
    
    @pytest.mark.asyncio
    async def test_store_api_key_redis_unavailable(self, redis_service):
        """Test API key storage when Redis is unavailable"""
        redis_service._client = None

        with patch.object(redis_service, '_ensure_connection', new_callable=AsyncMock) as mock_ensure_connection:
            mock_ensure_connection.return_value = False
            result = await redis_service.store_api_key("hash", "tenant")

        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_api_key_info_found(self, redis_service, mock_redis_client):
        """Test retrieving existing API key info"""
        api_key = "test-api-key"
        expected_data = {
            "tenant_id": "test_tenant",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        
        mock_redis_client.get.return_value = json.dumps(expected_data)
        
        result = await redis_service.get_api_key_info(api_key)
        
        assert result == expected_data
        mock_redis_client.get.assert_awaited_once()
    
    @pytest.mark.asyncio
    async def test_get_api_key_info_not_found(self, redis_service, mock_redis_client):
        """Test retrieving non-existent API key"""
        mock_redis_client.get.return_value = None
        
        result = await redis_service.get_api_key_info("nonexistent-key")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_api_key_info_redis_unavailable(self, redis_service):
        """Test API key retrieval when Redis is unavailable"""
        redis_service._client = None
        
        with patch.object(redis_service, '_ensure_connection', new_callable=AsyncMock) as mock_ensure_connection:
            mock_ensure_connection.return_value = False
            result = await redis_service.get_api_key_info("test-key")
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_revoke_api_key_success(self, redis_service, mock_redis_client):
        """Test successful API key revocation"""
        api_key_hash = "test_hash"
        existing_data = {
            "tenant_id": "test_tenant",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        
        mock_redis_client.get.return_value = json.dumps(existing_data)
        mock_redis_client.set.return_value = True
        
        result = await redis_service.revoke_api_key(api_key_hash)
        
        assert result is True
        
        call_args = mock_redis_client.set.call_args
        updated_data = json.loads(call_args[0][1])
        assert updated_data["revoked"] is True
    
    @pytest.mark.asyncio
    async def test_revoke_api_key_not_found(self, redis_service, mock_redis_client):
        """Test revoking non-existent API key"""
        mock_redis_client.get.return_value = None
        
        result = await redis_service.revoke_api_key("nonexistent_hash")
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_delete_api_key_success(self, redis_service, mock_redis_client):
        """Test successful API key deletion"""
        api_key_hash = "test_hash"
        mock_redis_client.delete.return_value = 1
        
        result = await redis_service.delete_api_key(api_key_hash)
        
        assert result is True
        mock_redis_client.delete.assert_awaited_once_with(f"api_key:{api_key_hash}")
    
    @pytest.mark.asyncio
    async def test_delete_api_key_not_found(self, redis_service, mock_redis_client):
        """Test deleting non-existent API key"""
        mock_redis_client.delete.return_value = 0
        
        result = await redis_service.delete_api_key("nonexistent_hash")
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_health_check_healthy(self, redis_service, mock_redis_client):
        """Test health check when Redis is healthy"""
        result = await redis_service.health_check()

        assert result is True
        mock_redis_client.ping.assert_awaited()
    
    @pytest.mark.asyncio
    async def test_health_check_unhealthy(self, redis_service):
        """Test health check when Redis is unavailable"""
        redis_service._client = None

        with patch.object(redis_service, '_ensure_connection', new_callable=AsyncMock) as mock_ensure_connection:
            mock_ensure_connection.return_value = False
            result = await redis_service.health_check()

        assert result is False
    
    @pytest.mark.asyncio
    @patch('app.core.redis.redis.from_url')
    async def test_connection_with_url(self, mock_from_url):
        """Test Redis connection using URL"""
        mock_client = AsyncMock()
        mock_client.ping.return_value = True
        mock_from_url.return_value = mock_client
        
        with patch('app.core.redis.settings.REDIS_URL', 'redis://test:6379'):
            service = RedisService()
            await service._connect()
            
        mock_from_url.assert_called_once()
        assert service._client == mock_client
    
    @pytest.mark.asyncio
    @patch('app.core.redis.redis.Redis')
    async def test_connection_fallback(self, mock_redis):
        """Test Redis connection fallback to localhost"""
        mock_client = AsyncMock()
        mock_client.ping.return_value = True
        mock_redis.return_value = mock_client
        
        with patch('app.core.redis.settings.REDIS_URL', None):
            service = RedisService()
            await service._connect()
            
        mock_redis.assert_called_once()
        assert service._client == mock_client


def test_get_redis_service():
    """Test global Redis service getter"""
    service = get_redis_service()
    assert isinstance(service, RedisService)
    
    # Should return same instance
    service2 = get_redis_service()
    assert service is service2