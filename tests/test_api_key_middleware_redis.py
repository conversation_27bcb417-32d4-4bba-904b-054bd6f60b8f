"""Tests for API Key Middleware with Redis integration"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import Fast<PERSON><PERSON>
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.engine import Result

from app.middleware.api_key import APIKeyMiddleware
from app.db.models import Tenant


@pytest.fixture
def app():
    """FastAPI app with middleware"""
    app = FastAPI()
    app.add_middleware(APIKeyMiddleware)
    
    @app.get("/test")
    async def test_endpoint():
        return {"message": "success"}
    
    @app.get("/health")
    async def health_endpoint():
        return {"status": "ok"}
    
    return app


@pytest.fixture
def client(app):
    """Test client"""
    return TestClient(app)


@pytest.fixture
def mock_tenant():
    """Mock tenant object"""
    tenant = Mock(spec=Tenant)
    tenant.id = "test-tenant-id"
    tenant.name = "Test Tenant"
    tenant.is_active = True
    tenant.api_key_hash = "hashed_api_key"
    return tenant


class TestAPIKeyMiddlewareRedis:
    
    @pytest.mark.asyncio
    async def test_bypass_paths(self, async_client):
        """Test that bypass paths work without API key"""
        response = await async_client.get("/health")
        assert response.status_code == 200

        response = await async_client.get("/docs")
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_missing_api_key(self, async_client):
        """Test request without API key"""
        response = await async_client.get("/test")
        assert response.status_code == 401
        assert response.json()["error_code"] == "MISSING_API_KEY"
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_redis_cache_hit(self, mock_get_redis, async_client, mock_tenant):
        """Test successful authentication via Redis cache"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "test-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local:
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session
            
            mock_result = AsyncMock(spec=Result)
            mock_result.scalar_one_or_none.return_value = mock_tenant
            mock_db_session.execute.return_value = mock_result

            # Create a simple test endpoint that goes through middleware
            from fastapi import FastAPI
            from app.main import app

            @app.get("/test-middleware")
            async def test_endpoint():
                return {"message": "success"}

            response = await async_client.get("/test-middleware", headers={"X-API-Key": "valid-api-key"})

            # If middleware works, we should get 200 from our test endpoint
            assert response.status_code == 200
            assert response.json() == {"message": "success"}
            assert response.json() == {"message": "success"}
            
            mock_redis.get_api_key_info.assert_awaited_once_with("valid-api-key")
            
            mock_db_session.execute.assert_awaited_once()
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_redis_cache_miss_db_hit(self, mock_get_redis, async_client, mock_tenant):
        """Test authentication via database fallback when Redis cache misses"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = None
        mock_redis._hash_api_key.return_value = "hashed_key"
        mock_redis.store_api_key.return_value = True
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session

            mock_result = AsyncMock(spec=Result)
            mock_result.scalars.return_value.all.return_value = [mock_tenant]
            mock_db_session.execute.return_value = mock_result
            
            mock_verify.return_value = True
            
            response = await async_client.get("/test-middleware", headers={"X-API-Key": "valid-api-key"})

            # If middleware works, we should get 200 from our test endpoint
            assert response.status_code == 200
            assert response.json() == {"message": "success"}
            
            mock_redis.get_api_key_info.assert_awaited_once_with("valid-api-key")
            
            mock_verify.assert_called_once_with("valid-api-key", "hashed_api_key")
            
            mock_redis.store_api_key.assert_awaited_once()
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_revoked_key_in_redis(self, mock_get_redis, async_client):
        """Test revoked key in Redis cache"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "test-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": True
        }
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session

            mock_result_all = AsyncMock(spec=Result)
            mock_result_all.scalars.return_value.all.return_value = []
            mock_db_session.execute.return_value = mock_result_all
            
            mock_verify.return_value = False
            
            response = await async_client.get("/test", headers={"X-API-Key": "revoked-api-key"})
            
            assert response.status_code == 401
            assert response.json()["error_code"] == "INVALID_API_KEY"
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_inactive_tenant(self, mock_get_redis, async_client):
        """Test inactive tenant in Redis cache"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "inactive-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        mock_redis.delete_api_key.return_value = True
        mock_redis._hash_api_key.return_value = "hashed_key"
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local:
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session

            inactive_tenant_mock = Mock(spec=Tenant)
            inactive_tenant_mock.is_active = False
            inactive_tenant_mock.id = "inactive-tenant-id"
            inactive_tenant_mock.name = "Inactive Tenant"

            # First call: get tenant by ID (returns inactive tenant)
            mock_result_first = AsyncMock(spec=Result)
            mock_result_first.scalar_one_or_none.return_value = inactive_tenant_mock

            # Second call: get all active tenants (returns empty list)
            mock_result_all = AsyncMock(spec=Result)
            mock_result_all.scalars.return_value.all.return_value = []

            # Configure side_effect to return different results for each call
            mock_db_session.execute.side_effect = [mock_result_first, mock_result_all]

            response = await async_client.get("/test-middleware", headers={"X-API-Key": "inactive-tenant-key"})

            # Should get 401 for inactive tenant
            assert response.status_code == 401
            
            mock_redis.delete_api_key.assert_awaited_once()
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_redis_unavailable_fallback(self, mock_get_redis, async_client, mock_tenant):
        """Test fallback to database when Redis is unavailable"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = None
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session

            mock_result_all = AsyncMock(spec=Result)
            mock_result_all.scalars.return_value.all.return_value = [mock_tenant]
            mock_db_session.execute.return_value = mock_result_all
            
            mock_verify.return_value = True
            
            response = await async_client.get("/test-middleware", headers={"X-API-Key": "valid-api-key"})

            # If middleware works, we should get 200 from our test endpoint
            assert response.status_code == 200
            assert response.json() == {"message": "success"}
            
            mock_verify.assert_called_once_with("valid-api-key", "hashed_api_key")
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_invalid_api_key(self, mock_get_redis, async_client):
        """Test completely invalid API key"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = None
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local, \
             patch('app.middleware.api_key.security.verify_password') as mock_verify:
            
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session

            mock_result_all = AsyncMock(spec=Result)
            mock_result_all.scalars.return_value.all.return_value = []
            mock_db_session.execute.return_value = mock_result_all
            
            mock_verify.return_value = False
            
            response = await async_client.get("/test", headers={"X-API-Key": "invalid-api-key"})
            
            assert response.status_code == 401
            assert response.json()["error_code"] == "INVALID_API_KEY"
    
    @pytest.mark.asyncio
    @patch('app.middleware.api_key.get_redis_service')
    async def test_tenant_context_injection(self, mock_get_redis, async_client, mock_tenant):
        """Test that tenant context is properly injected"""
        mock_redis = AsyncMock()
        mock_redis.get_api_key_info.return_value = {
            "tenant_id": "test-tenant-id",
            "created_at": "2025-01-01T00:00:00Z",
            "revoked": False
        }
        mock_get_redis.return_value = mock_redis
        
        with patch('app.middleware.api_key.SessionLocal') as mock_session_local:
            mock_db_session = AsyncMock(spec=AsyncSession)
            mock_session_local.return_value.__aenter__.return_value = mock_db_session

            mock_result_first = AsyncMock(spec=Result)
            mock_result_first.scalar_one_or_none.return_value = mock_tenant
            mock_db_session.execute.return_value = mock_result_first
            
            response = await async_client.get("/test-middleware", headers={"X-API-Key": "valid-api-key"})

            # If middleware works, we should get 200 from our test endpoint
            assert response.status_code == 200
            assert response.json() == {"message": "success"}

            # Check that tenant context was injected (these headers are set by the middleware)
            assert "X-Tenant-Name" in response.headers
            assert "X-Tenant-ID" in response.headers