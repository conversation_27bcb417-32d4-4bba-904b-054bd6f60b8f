import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient, ASGITransport
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy import text
import uuid
from unittest.mock import AsyncMock, patch

from app.main import app
from app.db.models import Base, User, Tenant
from app.core.security import get_password_hash
from app.api.v1 import deps
from app.core import security

# Override the dependency to use test database
# Use PostgreSQL for tests to match production environment and support ARRAY types
TEST_DATABASE_URL = "postgresql+psycopg_async://quanthea_admin:f496c6027f65676e8cb9d1350fc6fe52e21f330f33fcbc444e05e5dacad1b775@db:5432/auth_db"

engine = create_async_engine(TEST_DATABASE_URL)
TestingSessionLocal = async_sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest_asyncio.fixture(scope="function")
async def db():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    db_session = TestingSessionLocal()
    try:
        yield db_session
    finally:
        await db_session.close()
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)


# Override the get_db dependency
async def override_get_db():
    db_session = TestingSessionLocal()
    try:
        yield db_session
    finally:
        await db_session.close()


app.dependency_overrides[deps.get_db] = override_get_db


@pytest.fixture(scope="function")
def client():
    with TestClient(app) as c:
        yield c


@pytest_asyncio.fixture(scope="function")
async def mock_redis():
    """Mock Redis service for tests"""
    mock_redis = AsyncMock()

    # Mock successful Redis operations
    mock_redis.get_api_key_info.return_value = None  # Default: key not found in Redis
    mock_redis.store_api_key.return_value = True
    mock_redis.delete_api_key.return_value = True
    mock_redis.revoke_api_key.return_value = True
    mock_redis.health_check.return_value = {"status": "healthy"}
    mock_redis._ensure_connection.return_value = True

    with patch('app.core.redis.get_redis_service', return_value=mock_redis):
        with patch('app.middleware.api_key.get_redis_service', return_value=mock_redis):
            yield mock_redis


@pytest_asyncio.fixture(scope="function")
async def async_client(mock_redis):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://testserver") as ac:
        yield ac


@pytest_asyncio.fixture(scope="function")
async def test_tenant(db: AsyncSession):
    # Create a real API key and hash it
    test_api_key = "test_api_key_12345678901234567890"
    api_key_hash = security.get_password_hash(test_api_key)

    tenant = Tenant(
        name="Test Tenant",
        description="Test tenant for unit tests",
        is_active=True,
        api_key_hash=api_key_hash,
        allowed_frontend_origins=["http://localhost:3000", "http://localhost:8080"]
    )
    db.add(tenant)
    await db.commit()
    await db.refresh(tenant)

    # Store the plain API key for use in tests
    tenant._test_api_key = test_api_key
    return tenant


@pytest_asyncio.fixture(scope="function")
async def tenant_headers(test_tenant): # Changed to async def
    # Use the real API key that matches the hashed version in the database
    return {"X-API-Key": test_tenant._test_api_key}


@pytest_asyncio.fixture(scope="function")
async def test_user(db: AsyncSession, test_tenant):
    """Create a test user for authentication tests."""
    user = User(
        email="<EMAIL>",
        hashed_password=get_password_hash("password123"),
        tenant_id=test_tenant.id,
        is_active=True
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user


@pytest_asyncio.fixture(scope="function")
async def user_token_headers(test_user, test_tenant): # Changed to async def
    """Create authentication headers with JWT token AND API key for test user."""
    from app.core.security import create_access_token
    from datetime import timedelta

    access_token_expires = timedelta(minutes=30)
    token_data = {
        "sub": str(test_user.id),
        "email": test_user.email,
        "tenant_id": str(test_user.tenant_id)
    }

    access_token = create_access_token(
        data=token_data,
        expires_delta=access_token_expires
    )

    return {
        "Authorization": f"Bearer {access_token}",
        "X-API-Key": test_tenant._test_api_key
    }


@pytest_asyncio.fixture(scope="function")
async def test_admin_user(db: AsyncSession):
    """Create a test admin user for admin tests."""
    from app.db.models import AdminUser

    admin_user = AdminUser(
        email="<EMAIL>",
        hashed_password=get_password_hash("admin123"),
        is_active=True
    )
    db.add(admin_user)
    await db.commit()
    await db.refresh(admin_user)
    return admin_user


@pytest.fixture(scope="function")
def admin_token_headers(test_admin_user):
    """Create authentication headers with JWT token for admin user."""
    from app.core.security import create_access_token
    from datetime import timedelta

    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        subject=test_admin_user.email,
        expires_delta=access_token_expires
    )

    return {"Authorization": f"Bearer {access_token}"}


