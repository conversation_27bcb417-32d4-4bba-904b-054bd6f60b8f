services:
  app:
    build: .
    container_name: auth-service-backend
    ports:
      - "8002:8000"
    volumes:
      - .:/app
    env_file:
      - .env.dev
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - quanthea-corp-network

  db:
    image: postgres:16
    container_name: auth-service-db
    volumes:
      - auth_service_db_data:/var/lib/postgresql/data/
    env_file:
      - .env.dev
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - quanthea-corp-network

volumes:
  auth_service_db_data:

networks:
  quanthea-corp-network:
    name: quanthea-corp-network
    external: true
