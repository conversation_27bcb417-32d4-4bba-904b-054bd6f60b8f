"""
Logging Configuration for the Authentication Service

This module sets up a structured logging system for the application,
including console output, file logging, and a dedicated security logger.

Key Features:
    - Configures logging levels based on application settings.
    - Supports JSON formatting for logs, enhancing machine readability.
    - Implements file rotation for log files to manage disk space.
    - Provides a specialized `SecurityLogger` for auditing security-sensitive events.
    - Integrates with `loguru` for enhanced logging capabilities.
"""
import logging
import os
from loguru import logger
from app.core.config import settings


class InterceptHandler(logging.Handler):
    """
    Intercepts standard logging messages and redirects them to Loguru.
    """
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # Find caller from where loguru logs this message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


class SecurityLogger:
    """
    A dedicated logger for security-related events.
    Ensures that security events are logged with specific formatting and context.
    """
    def __init__(self):
        # Use the logger configured by setup_logging
        self.logger = logging.getLogger("security_audit")

    def info(self, message: str, extra: dict = None):
        self.logger.info(message, extra=extra)

    def warning(self, message: str, extra: dict = None):
        self.logger.warning(message, extra=extra)

    def error(self, message: str, extra: dict = None):
        self.logger.error(message, extra=extra)

    def debug(self, message: str, extra: dict = None):
        self.logger.debug(message, extra=extra)

    def log_login_attempt(self, request, email: str, success: bool, user_id: str = None, failure_reason: str = None):
        """
        Logs a user login attempt with detailed context.
        """
        log_level = "info" if success else "warning"
        message = "User login successful" if success else "User login failed"
        
        extra_data = {
            "component": "auth_login_attempt",
            "email": email,
            "success": success,
            "client_ip": request.client.host if request.client else "unknown",
            "user_agent": request.headers.get("User-Agent"),
            "tenant_id": getattr(request.state, 'tenant_id', 'unknown'),
            "tenant_name": getattr(request.state, 'tenant_name', 'unknown')
        }
        if user_id:
            extra_data["user_id"] = str(user_id)
        if failure_reason:
            extra_data["failure_reason"] = failure_reason
        
        getattr(self.logger, log_level)(message, extra=extra_data)


security_logger_instance = None

def get_security_logger():
    global security_logger_instance
    if security_logger_instance is None:
        security_logger_instance = SecurityLogger()
    return security_logger_instance


def setup_logging():
    """
    Configures the application's logging system.
    """
    # Remove default logger
    logger.remove()

    # Add console logger
    logger.add(
        os.sys.stderr,
        level=settings.get_log_level().value,
        format="{level.icon} <green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level> {extra}",
        colorize=True,
        backtrace=True,
        diagnose=True,
    )

    # Add file logger if enabled
    if settings.LOG_FILE_ENABLED:
        log_dir = settings.LOG_FILE_PATH
        os.makedirs(log_dir, exist_ok=True)
        
        logger.add(
            os.path.join(log_dir, "app.log"),
            rotation=f"{settings.LOG_FILE_MAX_SIZE} B",  # Rotate when file size exceeds max_size
            retention=settings.LOG_FILE_BACKUP_COUNT,    # Keep this many backup files
            level=settings.get_log_level().value,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} - {message} {extra}",
            enqueue=True,  # Use a queue for non-blocking writes
            serialize=True, # Output logs as JSON
        )
        
        # Dedicated security audit log
        logger.add(
            os.path.join(log_dir, "security_audit.log"),
            rotation="10 MB",
            retention="7 days",
            level="INFO", # Security logs should always be INFO or higher
            filter=lambda record: record["extra"].get("component") == "security" or record["extra"].get("component") == "auth_login_attempt",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {name}:{function}:{line} - {message} {extra}",
            enqueue=True,
            serialize=True,
        )

    # Intercept standard logging
    logging.basicConfig(handlers=[InterceptHandler()], level=0)
    logging.getLogger("uvicorn.access").handlers = [InterceptHandler()]
    logging.getLogger("uvicorn.error").handlers = [InterceptHandler()]
    logging.getLogger("sqlalchemy.engine").handlers = [InterceptHandler()]
    logging.getLogger("alembic").handlers = [InterceptHandler()]