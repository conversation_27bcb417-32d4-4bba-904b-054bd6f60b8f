"""Redis Service for API Key Management"""

import json
import hashlib
import ssl
import os
from typing import Optional, Dict, Any
from datetime import datetime, timezone

import redis.asyncio as redis
from redis.exceptions import ConnectionError, TimeoutError, RedisError

from app.core.config import settings
from loguru import logger


class RedisService:
    
    def __init__(self):
        """Initialize Redis service with connection."""
        self._client: Optional[redis.Redis] = None
        # _connect is now async, so it cannot be called directly in __init__
        # The connection will be established on first async use or via explicit connect call
    
    async def _connect(self) -> None:
        """
        Establish connection to Redis.
        
        Supports both URL-based connection (production) and
        fallback to localhost (development).
        """
        try:
            if settings.REDIS_URL:
                # Try TLS connection first
                if settings.REDIS_URL.startswith('rediss://'):
                    try:
                        logger.info("Attempting TLS connection to Redis...")

                        # Check for CA certificate file
                        ca_cert_path = None
                        possible_paths = [
                            '/app/redis_ca.pem',  # Render deployment
                            './redis_ca.pem',     # Local development
                            'redis_ca.pem'        # Current directory
                        ]

                        for path in possible_paths:
                            if os.path.exists(path):
                                ca_cert_path = path
                                logger.info(f"Found Redis CA certificate at: {path}")
                                break

                        # Configure TLS with or without CA certificate
                        tls_kwargs = {
                            'decode_responses': True,
                            'socket_connect_timeout': 10,
                            'socket_timeout': 10,
                            'retry_on_timeout': True,
                            'health_check_interval': 30
                        }

                        if ca_cert_path:
                            # Use proper TLS with CA certificate
                            tls_kwargs.update({
                                'ssl_ca_certs': ca_cert_path,
                                'ssl_cert_reqs': ssl.CERT_REQUIRED,
                                'ssl_check_hostname': True
                            })
                            logger.info("Using TLS with CA certificate verification")
                        else:
                            # Fallback to insecure TLS
                            tls_kwargs.update({
                                'ssl_cert_reqs': ssl.CERT_NONE,
                                'ssl_check_hostname': False,
                                'ssl_ca_certs': None
                            })
                            logger.warning("Using TLS without certificate verification")

                        self._client = redis.from_url(settings.REDIS_URL, **tls_kwargs)

                        # Test TLS connection
                        await self._client.ping()
                        logger.info("TLS connection to Redis successful")

                    except Exception as tls_error:
                        logger.warning(f"TLS connection failed: {tls_error}")
                        logger.info("Attempting non-TLS connection as fallback...")
                        # Fallback to non-TLS
                        non_tls_url = settings.REDIS_URL.replace('rediss://', 'redis://')
                        self._client = redis.from_url(
                            non_tls_url,
                            decode_responses=True,
                            socket_connect_timeout=10,
                            socket_timeout=10,
                            retry_on_timeout=True,
                            health_check_interval=30
                        )
                else:
                    # Non-TLS connection
                    self._client = redis.from_url(
                        settings.REDIS_URL,
                        decode_responses=True,
                        socket_connect_timeout=10,
                        socket_timeout=10,
                        retry_on_timeout=True,
                        health_check_interval=30
                    )
            else:
                # Fallback for development
                self._client = redis.Redis(
                    host='localhost',
                    port=6379,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True
                )
            
            # Test connection
            await self._client.ping()
            logger.info("Redis connection established successfully")
            
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._client = None
        except Exception as e:
            logger.error(f"Unexpected error connecting to Redis: {e}")
            self._client = None
    
    async def _ensure_connection(self) -> bool:
        """
        Ensure Redis connection is active.
        
        Returns:
            bool: True if connection is active, False otherwise
        """
        if not self._client:
            await self._connect()
        
        if self._client:
            try:
                await self._client.ping()
                return True
            except (ConnectionError, TimeoutError):
                logger.warning("Redis connection lost, attempting to reconnect...")
                await self._connect()
                return self._client is not None
        
        return False
    
    def _hash_api_key(self, api_key: str) -> str:
        """
        Generate SHA-256 hash of API key.
        
        Args:
            api_key: The raw API key string
            
        Returns:
            str: SHA-256 hash of the API key
        """
        return hashlib.sha256(api_key.encode()).hexdigest()
    
    async def store_api_key(self, api_key_hash: str, tenant_id: str, created_at: Optional[datetime] = None) -> bool:
        """
        Store API key hash with tenant information in Redis.
        
        Args:
            api_key_hash: SHA-256 hash of the API key
            tenant_id: UUID of the tenant
            created_at: When the key was created (defaults to now)
            
        Returns:
            bool: True if stored successfully, False otherwise
        """
        if not await self._ensure_connection():
            logger.error("Cannot store API key: Redis connection unavailable")
            return False
        
        try:
            key = f"api_key:{api_key_hash}"
            value = {
                "tenant_id": tenant_id,
                "created_at": (created_at or datetime.now(timezone.utc)).isoformat(),
                "revoked": False
            }
            
            result = await self._client.set(key, json.dumps(value))
            
            if result:
                logger.debug(f"API key hash stored for tenant {tenant_id}")
                return True
            else:
                logger.error(f"Failed to store API key hash for tenant {tenant_id}")
                return False
                
        except RedisError as e:
            logger.error(f"Redis error storing API key: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error storing API key: {e}")
            return False
    
    async def get_api_key_info(self, api_key: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve API key information by raw API key.
        
        Args:
            api_key: The raw API key string
            
        Returns:
            Dict with tenant_id, created_at, revoked status, or None if not found
        """
        if not await self._ensure_connection():
            logger.error("Cannot retrieve API key: Redis connection unavailable")
            return None
        
        try:
            api_key_hash = self._hash_api_key(api_key)
            key = f"api_key:{api_key_hash}"
            
            value = await self._client.get(key)
            
            if value:
                data = json.loads(value)
                logger.debug(f"API key found for tenant {data.get('tenant_id')}")
                return data
            else:
                logger.debug("API key not found in Redis cache")
                return None
                
        except (json.JSONDecodeError, RedisError) as e:
            logger.error(f"Error retrieving API key: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error retrieving API key: {e}")
            return None
    
    async def revoke_api_key(self, api_key_hash: str) -> bool:
        """
        Mark an API key as revoked.
        
        Args:
            api_key_hash: SHA-256 hash of the API key
            
        Returns:
            bool: True if revoked successfully, False otherwise
        """
        if not await self._ensure_connection():
            logger.error("Cannot revoke API key: Redis connection unavailable")
            return False
        
        try:
            key = f"api_key:{api_key_hash}"
            value = await self._client.get(key)
            
            if value:
                data = json.loads(value)
                data["revoked"] = True
                
                result = await self._client.set(key, json.dumps(data))
                
                if result:
                    logger.info(f"API key revoked for tenant {data.get('tenant_id')}")
                    return True
                else:
                    logger.error("Failed to update API key revocation status")
                    return False
            else:
                logger.warning("Attempted to revoke non-existent API key")
                return False
                
        except (json.JSONDecodeError, RedisError) as e:
            logger.error(f"Error revoking API key: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error revoking API key: {e}")
            return False
    
    async def delete_api_key(self, api_key_hash: str) -> bool:
        """
        Delete an API key from Redis.
        
        Args:
            api_key_hash: SHA-256 hash of the API key
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        if not await self._ensure_connection():
            logger.error("Cannot delete API key: Redis connection unavailable")
            return False
        
        try:
            key = f"api_key:{api_key_hash}"
            result = await self._client.delete(key)
            
            if result:
                logger.info("API key deleted from Redis")
                return True
            else:
                logger.warning("Attempted to delete non-existent API key")
                return False
                
        except RedisError as e:
            logger.error(f"Error deleting API key: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error deleting API key: {e}")
            return False
    
    async def revoke_tenant_api_keys(self, tenant_id: str) -> bool:
        """
        Revoke all API keys for a specific tenant.

        Args:
            tenant_id: UUID of the tenant whose keys should be revoked

        Returns:
            bool: True if revocation was successful, False otherwise
        """
        if not await self._ensure_connection():
            logger.error("Cannot revoke API keys: Redis connection unavailable")
            return False

        try:
            # Find all API keys for this tenant
            pattern = "api_key:*"
            keys = await self._client.keys(pattern)

            revoked_count = 0
            for key in keys:
                try:
                    value = await self._client.get(key)
                    if value:
                        data = json.loads(value)
                        if data.get("tenant_id") == tenant_id:
                            # Mark as revoked instead of deleting to maintain audit trail
                            data["revoked"] = True
                            await self._client.set(key, json.dumps(data))
                            revoked_count += 1
                except Exception as e:
                    logger.error(f"Error revoking key {key}: {e}")
                    continue

            logger.info(f"Revoked {revoked_count} API keys for tenant {tenant_id}")
            return True

        except RedisError as e:
            logger.error(f"Redis error revoking API keys for tenant {tenant_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error revoking API keys for tenant {tenant_id}: {e}")
            return False

    async def health_check(self) -> bool:
        """
        Check if Redis is healthy and responsive.

        Returns:
            bool: True if Redis is healthy, False otherwise
        """
        try:
            if await self._ensure_connection():
                await self._client.ping()
                return True
            return False
        except Exception:
            return False


# Global Redis service instance
# The instance needs to be created in an async context or managed differently
# For now, we'll keep it as is, but be aware that _connect won't be called on init
redis_service = RedisService()


def get_redis_service() -> RedisService:
    """
    Get the global Redis service instance.
    
    Returns:
        RedisService: The global Redis service instance
    """
    return redis_service
