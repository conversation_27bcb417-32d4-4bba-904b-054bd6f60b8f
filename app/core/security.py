"""
Cryptographic and Security Utilities

This module provides core security functions for the application, including
password hashing, JWT token creation, and token verification.

Functions:
    create_access_token: Creates a new JWT access token.
    verify_password: Verifies a plain-text password against a hash.
    get_password_hash: Hashes a plain-text password.
    generate_password_reset_token: Generates a secure URL-safe token.
    create_password_reset_token: Creates a JWT specifically for password resets.
    verify_password_reset_token: Verifies a password reset JWT.

Key Features:
    - Strong password hashing using Argon2.
    - JWT generation and validation for authentication.
    - Secure token generation for password resets.
    - Centralized management of security constants and algorithms.
"""
from datetime import datetime, timedelta, timezone
from typing import Any, Union, Optional
import secrets

from jose import jwt, J<PERSON><PERSON><PERSON><PERSON>
from argon2 import PasswordHasher

from app.core.config import settings

ph = PasswordHasher()


def create_access_token(
    data: Union[dict, str, Any] = None,
    subject: Union[str, Any] = None,
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Create JWT access token.

    Args:
        data: Dictionary with token data (preferred) or legacy subject
        subject: Legacy parameter for backward compatibility
        expires_delta: Token expiration time

    Returns:
        Encoded JWT token
    """
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    if isinstance(data, dict):
        to_encode = data.copy()
        to_encode.update({"exp": expire})
    else:
        subject_value = data if data is not None else subject
        to_encode = {"exp": expire, "sub": str(subject_value)}

    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    try:
        ph.verify(hashed_password, plain_password)
        return True
    except Exception:
        return False


def get_password_hash(password: str) -> str:
    return ph.hash(password)


def generate_password_reset_token() -> str:
    """
    Generates a secure token for password reset.
    """
    return secrets.token_urlsafe(32)


def create_password_reset_token(email: str) -> str:
    """
    Creates a JWT token for password reset.
    """
    delta = timedelta(hours=settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.now(timezone.utc)
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now.timestamp(), "sub": email, "type": "password_reset"},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """
    Verifies a password reset token and returns the email if valid.
    """
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        if decoded_token.get("type") != "password_reset":
            return None
        return decoded_token["sub"]
    except JWTError:
        return None