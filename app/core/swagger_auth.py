"""
Swagger UI Authentication and Documentation Utilities

This module configures the API key authentication for the Swagger UI, making it easier
to test protected endpoints directly from the documentation.

Functions:
    get_api_key_for_docs: A dependency for documenting the API key header.
    validate_api_key_for_docs: A dependency that enforces the API key in Swagger.
    get_tenant_info: Retrieves tenant information for documentation purposes.

Key Features:
    - Provides a clear and user-friendly way to use API keys in Swagger.
    - Includes detailed descriptions and example keys for testing.
    - Separates documentation dependencies from the actual application logic.
"""
from fastapi import HTTPException, Depends
from fastapi.security import APIKeyHeader
from fastapi.openapi.models import <PERSON>Key
from typing import Optional

from app.core.config import settings

api_key_header = APIKeyHeader(
    name="X-API-Key",
    description="""
    **API Key for tenant authentication**
    
    Each client application has its own API key that maps to a specific tenant.
    
    **Available API Keys for testing:**
    
    🌐 **Frontend Web App:**
    ```
    ak_frontend_web_1234567890abcdef1111111111111111
    ```
    
    📱 **Mobile Application:**
    ```
    ak_mobile_app_9876543210fedcba2222222222222222
    ```
    
    ⚙️ **Admin Panel:**
    ```
    ak_admin_panel_5555666677778888333333333333333
    ```
    
    📊 **Analytics Service:**
    ```
    ak_analytics_aaaaaabbbbbbcccc4444444444444444
    ```
    
    🔗 **Webhook Service:**
    ```
    ak_webhook_dddddeeeeefffff005555555555555555
    ```
    
    **Usage:**
    1. Copy one of the API keys above
    2. Click the "Authorize" button (🔒) at the top of this page
    3. Paste the API key in the "Value" field
    4. Click "Authorize"
    5. Now you can test the protected endpoints
    
    **Note:** Users are isolated by tenant. The same email can exist in different tenants.
    """,
    auto_error=False  # Don't auto-raise errors, let middleware handle it
)


def get_api_key_for_docs(api_key: Optional[str] = Depends(api_key_header)) -> Optional[str]:
    """
    Dependency for Swagger documentation.
    
    This function is for documentation purposes only - the actual validation is done by the middleware.
    The APIKeyMiddleware already validates the API key before this function is called.
    """
    return api_key


def validate_api_key_for_docs(api_key: str = Depends(api_key_header)) -> str:
    """
    Dependency that documents the need for an API key in Swagger.
    
    The actual validation is done by APIKeyMiddleware, but this function
    ensures that Swagger shows the API key field as required.
    """
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail="API key required in X-API-Key header"
        )
    return api_key


# Tenant information for documentation
TENANT_INFO = {
    "7c387a2e-91b6-4acc-8699-f581316f2cf2": {
        "name": "Frontend Web App",
        "description": "Main web application",
        "api_key_prefix": "ak_frontend_web_"
    },
    "42e2bd52-28fd-46cc-94cf-b60f552ebd9a": {
        "name": "Mobile Application",
        "description": "iOS/Android mobile app",
        "api_key_prefix": "ak_mobile_app_"
    },
    "45deda29-fe37-421e-a468-a8eec822caaa": {
        "name": "Admin Panel",
        "description": "Administrative dashboard",
        "api_key_prefix": "ak_admin_panel_"
    },
    "c8f9ba87-53fa-481e-aea1-47eccc1c0b43": {
        "name": "Analytics Service",
        "description": "Data analytics service",
        "api_key_prefix": "ak_analytics_"
    },
    "c00984ef-8bea-4700-8714-6e93b3d87220": {
        "name": "Webhook Service",
        "description": "External webhook handler",
        "api_key_prefix": "ak_webhook_"
    }
}


def get_tenant_info(tenant_id: str) -> dict:
    """Get tenant information for documentation"""
    return TENANT_INFO.get(tenant_id, {
        "name": "Unknown Tenant",
        "description": "Unknown tenant",
        "api_key_prefix": "unknown_"
    })
