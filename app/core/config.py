"""
Application Configuration Management

This module defines the configuration structure for the application using Pydantic's BaseSettings.
It loads settings from environment variables and a .env file, providing a centralized
and type-hinted way to access configuration values throughout the service.

Classes:
    Environment: Enum for deployment environments (development, staging, production).
    LogLevel: Enum for logging levels.
    Settings: Main configuration class holding all application settings.

Key Features:
    - Centralized settings management.
    - Type validation for configuration parameters.
    - Loading from .env files and environment variables.
    - Dynamic configuration based on the current environment (e.g., CORS, log levels).
    - Secure handling of secrets and API keys.
"""
from pydantic_settings import BaseSettings
from pydantic import ConfigDict
from typing import Optional
from enum import Enum
import os


class Environment(str, Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """Log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class Settings(BaseSettings):
    model_config = ConfigDict(
        env_file=".env.dev",
        extra="ignore"
    )

    DATABASE_URL: str

    @property
    def async_database_url(self) -> str:
        """
        Ensure DATABASE_URL uses asyncpg driver for async operations.
        Converts postgresql:// to postgresql+asyncpg:// if needed.
        """
        if self.DATABASE_URL.startswith("postgresql://"):
            return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://", 1)
        elif self.DATABASE_URL.startswith("postgres://"):
            return self.DATABASE_URL.replace("postgres://", "postgresql+asyncpg://", 1)
        return self.DATABASE_URL

    SECRET_KEY: str
    ALGORITHM: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = 24

    SMTP_HOST: str = "smtp.hostinger.com"
    SMTP_PORT: int = 465
    SMTP_USE_SSL: bool = True
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None

    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None


    ADMIN_FRONTEND_URL: Optional[str] = None
    FRONTEND_URL: Optional[str] = None
    TRUSTED_FRONTEND_ORIGINS: Optional[str] = None

    # Redis Configuration
    REDIS_URL: Optional[str] = None

    ENVIRONMENT: Environment = Environment.DEVELOPMENT
    LOG_LEVEL: Optional[LogLevel] = None
    LOG_FILE_ENABLED: bool = True
    LOG_FILE_PATH: str = "logs"
    LOG_FILE_MAX_SIZE: int = 10485760
    LOG_FILE_BACKUP_COUNT: int = 5

    RATE_LIMIT_ENABLED: bool = True
    SECURITY_HEADERS_ENABLED: bool = True

    def get_log_level(self) -> LogLevel:
        """Get log level based on environment and explicit setting"""
        # Explicit log level override
        if self.LOG_LEVEL:
            return self.LOG_LEVEL

        # Default levels by environment
        if self.ENVIRONMENT == Environment.PRODUCTION:
            return LogLevel.INFO
        elif self.ENVIRONMENT == Environment.STAGING:
            return LogLevel.INFO
        else:  # DEVELOPMENT
            return LogLevel.DEBUG

    @property
    def is_development(self) -> bool:
        """Check if running in development"""
        return self.ENVIRONMENT == Environment.DEVELOPMENT

    @property
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.ENVIRONMENT == Environment.PRODUCTION

    @property
    def is_staging(self) -> bool:
        """Check if running in staging"""
        return self.ENVIRONMENT == Environment.STAGING

    def get_cors_origins(self) -> list:
        """Get CORS origins based on environment"""
        if self.is_production:
            # Production: specific origins only
            origins = set()
            if os.getenv("CORS_ORIGINS"):
                origins.update([origin.strip() for origin in os.getenv("CORS_ORIGINS").split(",")])
            
            if self.ADMIN_FRONTEND_URL:
                origins.add(self.ADMIN_FRONTEND_URL)
            return list(origins)
        else:
            # Development/Staging: allow all for easier testing
            return ["*"]

    def get_trusted_frontend_origins(self) -> list:
        """Get trusted frontend origins for password reset links."""
        if self.TRUSTED_FRONTEND_ORIGINS:
            return [origin.strip() for origin in self.TRUSTED_FRONTEND_ORIGINS.split(",")]
        return []

settings = Settings()