"""
Email Sending Utilities

This module provides functions for sending emails using an SMTP server.
It uses Jinja2 for templating to create rich HTML emails.

Functions:
    send_email: Sends a generic email using a specified template.
    send_password_reset_email: Sends a password reset email to a user.

Key Features:
    - Asynchronous email sending with aiosmtplib.
    - HTML email templating with Jinja2.
    - Secure SMTP connection using SSL/TLS.
    - Centralized configuration via the Settings class.
"""
import logging
import ssl
from pathlib import Path
from typing import Any, Dict

import aiosmtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from jinja2 import Environment, select_autoescape, FileSystemLoader

from app.core.config import settings


logger = logging.getLogger(__name__)

templates_dir = Path(__file__).parent.parent / "templates"
if not templates_dir.exists():
    templates_dir.mkdir(parents=True)

env = Environment(
    loader=FileSystemLoader(templates_dir),
    autoescape=select_autoescape(['html', 'xml'])
)


async def send_email(
    email_to: str,
    subject: str,
    template_name: str,
    template_data: Dict[str, Any] = {}
) -> None:
    """
    Sends an email using the specified template via SMTP Hostinger.
    """
    if not settings.SMTP_USER or not settings.SMTP_PASSWORD:
        logger.warning("SMTP credentials not configured. Email not sent.")
        return
    
    # Prepare email content
    try:
        template = env.get_template(f"{template_name}.html")
        html_content = template.render(**template_data)
    except Exception as e:
        logger.error(f"Error loading template: {e}")
        html_content = f"""
        <html>
            <body>
                <p>Hello,</p>
                <p>{template_data.get('message', 'System message.')}</p>
                <p>Regards,<br>{settings.EMAILS_FROM_NAME}</p>
            </body>
        </html>
        """
    
    # Prepare the email
    message = MIMEMultipart()
    message["From"] = f"{settings.EMAILS_FROM_NAME} <{settings.EMAILS_FROM_EMAIL}>"
    message["To"] = email_to
    message["Subject"] = subject
    
    # Attach HTML content to the email
    message.attach(MIMEText(html_content, "html"))
    
    # Send email via SMTP Hostinger
    try:
        # SSL configuration for Hostinger
        context = ssl.create_default_context()
        
        smtp = aiosmtplib.SMTP(
            hostname=settings.SMTP_HOST,
            port=settings.SMTP_PORT,
            use_tls=True,  # Use direct SSL/TLS
            tls_context=context
        )
        
        # Connect with direct SSL (port 465)
        await smtp.connect()
        
        # Authenticate
        await smtp.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
        
        # Send message
        await smtp.send_message(message)
        await smtp.quit()
        
        logger.info(f"Email sent via SMTP Hostinger to {email_to}")
        
    except Exception as e:
        logger.error(f"Error sending email via SMTP Hostinger: {e}")
        import traceback
        traceback.print_exc() # Print full traceback to stderr
        raise


async def send_password_reset_email(email_to: str, token: str, frontend_url: str) -> None:
    """
    Sends a password reset email.
    """
    reset_link = f"{frontend_url}/reset-password?token={token}"
    
    template_data = {
        "reset_link": reset_link,
        "valid_hours": settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS
    }
    
    await send_email(
        email_to=email_to,
        subject="Password Reset",
        template_name="password_reset",
        template_data=template_data
    )
