from sqlalchemy import <PERSON><PERSON><PERSON>, Column, String, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, TIMESTAMP, ARRAY # Importar ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import sqlalchemy as sa

from .base import Base


class TimestampMixin:
    """
    Mixin to add standardized audit fields.

    Adds:
    - created_at: Creation timestamp (UTC, immutable)
    - updated_at: Last update timestamp (UTC, auto-updated)

    Best practices:
    - Uses UTC timezone for global consistency
    - created_at is immutable after creation
    - updated_at is automatically updated via onupdate
    """
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="Record creation timestamp (UTC)"
    )

    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="Last update timestamp (UTC)"
    )


class Tenant(Base, TimestampMixin):
    """
    Model for clients/applications using the authentication service.

    Each tenant represents a client application with its own users.
    Full data isolation between tenants.
    """
    __tablename__ = "tenants"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
        comment="Unique identifier for the tenant"
    )
    name = Column(
        String(255),
        nullable=False,
        comment="Name of the tenant/application"
    )
    description = Column(
        Text,
        nullable=True,
        comment="Description of the tenant/application"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether the tenant is active"
    )
    api_key_hash = Column(
        String(255),
        nullable=True,
        comment="Hashed API key for the tenant"
    )
    allowed_frontend_origins = Column(
        ARRAY(String),
        nullable=False,
        server_default='{}', # Default para lista vazia no PostgreSQL
        comment="List of trusted frontend origins for the tenant"
    )

    users = relationship("User", back_populates="tenant", cascade="all, delete-orphan")

    __table_args__ = (
        sa.UniqueConstraint('name', name='uq_tenant_name'),
    )


class User(Base, TimestampMixin):
    """
    Model for system users.

    Each user belongs to a specific tenant.
    Email must be unique per tenant (not globally).
    """
    __tablename__ = "users"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
        comment="Unique identifier for the user"
    )
    tenant_id = Column(
        UUID(as_uuid=True),
        ForeignKey("tenants.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="ID of the tenant to which the user belongs"
    )
    email = Column(
        String(255),
        nullable=False,
        index=True,
        comment="User's email (unique per tenant)"
    )
    hashed_password = Column(
        String(255),
        nullable=False,
        comment="Hash of the user's password"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether the user is active"
    )
    reset_token = Column(
        String(255),
        nullable=True,
        comment="Token for password reset"
    )
    reset_token_expires = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="Expiration of the reset token"
    )

    tenant = relationship("Tenant", back_populates="users")

    # Constraints
    __table_args__ = (
        sa.UniqueConstraint('tenant_id', 'email', name='uq_tenant_email'),
        sa.Index('idx_tenant_user_active', 'tenant_id', 'is_active'),
        sa.Index('idx_user_email_lookup', 'tenant_id', 'email'),
    )


class AdminUser(Base, TimestampMixin):
    """
    Model for administrative users.

    These users manage the system, including tenant API keys.
    """
    __tablename__ = "admin_users"

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True,
        comment="Unique identifier for the admin user"
    )
    email = Column(
        String(255),
        nullable=False,
        unique=True,
        index=True,
        comment="Admin user's email (unique globally)"
    )
    hashed_password = Column(
        String(255),
        nullable=False,
        comment="Hash of the admin user's password"
    )
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether the admin user is active"
    )
