"""
Redis-based Distributed Rate Limiting Middleware

This module provides a distributed rate limiting middleware for FastAPI using Redis.
It replaces the in-memory SimpleRateLimitMiddleware to work properly in distributed
environments and follows the QUANTHEACORP.md guidelines.

Classes:
    RedisRateLimiter: Redis-based implementation of a rate limiter.
    RedisRateLimitMiddleware: FastAPI middleware that applies distributed rate limiting.

Key Features:
    - Distributed rate limiting using Redis for storage.
    - Tracks requests by client IP and endpoint across multiple instances.
    - Configurable limits and time windows for different endpoints.
    - Returns a 429 Too Many Requests response when limits are exceeded.
    - Graceful fallback when Redis is unavailable.
    - Integrates with the application's security logging.
"""
import time
from typing import Dict, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.redis import get_redis_service
from app.core.logging import get_security_logger
from app.core.config import settings


class RedisRateLimiter:
    """Redis-based distributed rate limiter"""
    
    def __init__(self):
        self.redis_service = get_redis_service()
        self.logger = get_security_logger()
    
    async def is_allowed(self, key: str, limit: int, window_seconds: int) -> bool:
        """
        Check if request is allowed using Redis sliding window.
        
        Args:
            key: Unique identifier for the rate limit (e.g., "ip:endpoint")
            limit: Maximum number of requests allowed
            window_seconds: Time window in seconds
            
        Returns:
            bool: True if request is allowed, False if rate limit exceeded
        """
        try:
            if not await self.redis_service._ensure_connection():
                # Fallback: allow request if Redis is unavailable
                self.logger.warning(
                    "Redis unavailable for rate limiting, allowing request",
                    extra={"component": "redis_rate_limit", "key": key}
                )
                return True
            
            now = time.time()
            pipeline = self.redis_service._client.pipeline()
            
            # Use Redis sorted set for sliding window
            redis_key = f"rate_limit:{key}"
            
            # Remove expired entries
            pipeline.zremrangebyscore(redis_key, 0, now - window_seconds)
            
            # Count current requests in window
            pipeline.zcard(redis_key)
            
            # Add current request
            pipeline.zadd(redis_key, {str(now): now})
            
            # Set expiration for cleanup
            pipeline.expire(redis_key, window_seconds + 1)
            
            results = await pipeline.execute()
            current_count = results[1]  # Result of zcard
            
            if current_count >= limit:
                # Remove the request we just added since it's not allowed
                await self.redis_service._client.zrem(redis_key, str(now))
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(
                f"Redis rate limiting error: {e}",
                extra={"component": "redis_rate_limit", "key": key}
            )
            # Fallback: allow request on error
            return True


class RedisRateLimitMiddleware(BaseHTTPMiddleware):
    """Distributed rate limiting middleware using Redis"""
    
    def __init__(self, app):
        super().__init__(app)
        self.limiter = RedisRateLimiter()
        self.logger = get_security_logger()
        
        # Rate limit configurations
        self.rate_limits = {
            "/api/v1/auth/login": {"limit": 5, "window": 900},  # 5 attempts per 15 minutes
            "/api/v1/auth/register": {"limit": 10, "window": 3600},  # 10 registrations per hour
            "/api/v1/auth/forgot-password": {"limit": 3, "window": 3600},  # 3 password resets per hour
            "/api/v1/admin/login": {"limit": 3, "window": 900},  # 3 admin attempts per 15 minutes
        }
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request headers"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection IP
        return request.client.host if request.client else "unknown"
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Process request with rate limiting"""
        path = request.url.path
        
        # Only apply rate limiting to configured endpoints
        if path not in self.rate_limits:
            return await call_next(request)
        
        # Skip rate limiting if disabled in settings
        if not settings.RATE_LIMIT_ENABLED:
            return await call_next(request)
        
        config = self.rate_limits[path]
        client_ip = self._get_client_ip(request)
        rate_limit_key = f"{client_ip}:{path}"
        
        self.logger.debug(
            f"Rate limit check for {client_ip} on {path}",
            extra={
                "component": "redis_rate_limit",
                "client_ip": client_ip,
                "endpoint": path,
                "limit": config["limit"],
                "window": config["window"]
            }
        )
        
        try:
            is_allowed = await self.limiter.is_allowed(
                rate_limit_key,
                config["limit"],
                config["window"]
            )
            
            if not is_allowed:
                self.logger.warning(
                    "Rate limit exceeded",
                    extra={
                        "component": "redis_rate_limit",
                        "client_ip": client_ip,
                        "endpoint": path,
                        "limit": config["limit"],
                        "window": config["window"],
                        "event": "rate_limit_exceeded"
                    }
                )
                
                return JSONResponse(
                    status_code=429,
                    content={
                        "detail": "Too many requests. Please try again later.",
                        "error_code": "RATE_LIMIT_EXCEEDED"
                    },
                    headers={"Retry-After": str(config["window"])}
                )
            
            self.logger.debug(
                f"Rate limit check passed for {client_ip} on {path}",
                extra={
                    "component": "redis_rate_limit",
                    "client_ip": client_ip,
                    "endpoint": path
                }
            )
            
        except Exception as e:
            self.logger.error(
                f"Rate limiting system error: {e}",
                extra={
                    "component": "redis_rate_limit",
                    "client_ip": client_ip,
                    "endpoint": path
                }
            )
            # Continue on error - don't block legitimate requests
        
        # Process the request
        response = await call_next(request)
        return response
