"""
API Key Authentication Middleware

This module provides a multi-tenant API Key authentication middleware for FastAPI.
It validates API keys, maps them to specific tenants, and injects tenant information
into the request context for downstream use.

Classes:
    APIKeyMiddleware: The core middleware class for API key authentication.
    TenantContextError: Custom exception for tenant context issues.

Functions:
    get_current_tenant_id: Dependency to retrieve the current tenant ID from the request.
    get_current_tenant_name: Dependency to retrieve the current tenant name from the request.
    get_tenant_context: Dependency to retrieve the full tenant context from the request.

Key Features:
    - Enforces API key presence for protected endpoints.
    - Supports bypassing authentication for public paths (e.g., health checks, documentation).
    - Integrates with the application's security logging for audit trails.
    - Provides tenant isolation by injecting tenant_id into the request state.
"""
from typing import Callable, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.logging import get_security_logger
from app.core.redis import get_redis_service
from app.db.base import SessionLocal
from app.db.models import Tenant
from app.core import security


class APIKeyMiddleware(BaseHTTPMiddleware):
    
    def __init__(self, app, bypass_paths: Optional[list] = None):
        super().__init__(app)
        self.bypass_paths = bypass_paths or [
            "/",
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico"
        ]
        
        get_security_logger().info(
            f"APIKeyMiddleware initialized. Bypass paths: {self.bypass_paths}",
            extra={
                "component": "api_key_middleware",
                "bypass_paths": self.bypass_paths
            }
        )
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Processes the request, validating the API-Key and injecting tenant context.
        """
        path = request.url.path
        method = request.method
        client_ip = self._get_client_ip(request)
        
        get_security_logger().debug(f"APIKeyMiddleware: Path: {path}, Method: {method}")
        
        if self._should_bypass(path):
            get_security_logger().debug(f"APIKeyMiddleware: Bypassing path: {path}")
            return await call_next(request)
        
        api_key = request.headers.get("X-API-Key")
        get_security_logger().debug(f"APIKeyMiddleware: X-API-Key: {api_key}")
        
        if not api_key:
            get_security_logger().debug(f"APIKeyMiddleware: Missing API Key, returning 401")
            get_security_logger().warning(
                "API key missing in request",
                extra={
                    "component": "api_key_middleware",
                    "path": path,
                    "method": method,
                    "client_ip": client_ip,
                    "error": "missing_api_key"
                }
            )
            return JSONResponse(
                status_code=401,
                content={
                    "detail": "API key required. Include X-API-Key header.",
                    "error_code": "MISSING_API_KEY"
                }
            )
        
        # Push-based approach: Use only Redis for API key validation
        redis_service = get_redis_service()
        api_key_info = await redis_service.get_api_key_info(api_key)

        tenant = None

        try:
            if api_key_info and not api_key_info.get("revoked", False):
                # API key found in Redis and not revoked
                tenant_id = api_key_info.get("tenant_id")

                # Get tenant details from database to verify it's still active
                async with SessionLocal() as db:
                    result = await db.execute(
                        select(Tenant).filter(
                            Tenant.id == tenant_id,
                            Tenant.is_active == True
                        )
                    )
                    tenant = result.scalar_one_or_none()

                if not tenant:
                    # Tenant not found or inactive, mark as revoked in Redis
                    await redis_service.revoke_api_key(redis_service._hash_api_key(api_key))
                    tenant = None

            if not tenant:
                # Log access attempt with invalid API-Key
                get_security_logger().warning(
                    "Invalid API key attempted",
                    extra={
                        "component": "api_key_middleware",
                        "path": path,
                        "method": method,
                        "client_ip": client_ip,
                        "api_key_prefix": api_key[:8] + "..." if len(api_key) > 8 else "***",
                        "error": "invalid_api_key"
                    }
                )
                return JSONResponse(
                    status_code=401,
                    content={
                        "detail": "Invalid API key.",
                        "error_code": "INVALID_API_KEY"
                    }
                )
            
            # Log authorized access
            get_security_logger().info(
                "API key authenticated successfully",
                extra={
                    "component": "api_key_middleware",
                    "path": path,
                    "method": method,
                    "client_ip": client_ip,
                    "tenant_id": str(tenant.id),
                    "tenant_name": tenant.name,
                    "api_key_prefix": api_key[:8] + "..." if len(api_key) > 8 else "***"
                }
            )
            
            # Inject tenant context into the request
            request.state.tenant_id = str(tenant.id)
            request.state.tenant_name = tenant.name
            request.state.api_key_prefix = api_key[:8] + "..." if len(api_key) > 8 else "***"
            
            # Continue processing
            response = await call_next(request)
            
            # Add informational headers to the response (optional)
            if hasattr(request.state, 'tenant_name'):
                response.headers["X-Tenant-Name"] = request.state.tenant_name
                response.headers["X-Tenant-ID"] = request.state.tenant_id
            
            return response
        finally:
            # db is now managed by async with, no explicit close needed here
            pass
    
    def _should_bypass(self, path: str) -> bool:
        """
        Checks if the path should be ignored by authentication.
        """
        for bypass_path in self.bypass_paths:
            # Special case for root path - exact match only
            if bypass_path == "/" and path == "/":
                return True
            # For other paths, use startswith but not for root
            elif bypass_path != "/" and path.startswith(bypass_path):
                return True
        return False
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Extracts client IP considering proxies.
        """
        # Check proxy headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct IP
        return request.client.host if request.client else "unknown"


class TenantContextError(Exception):
    """Exception for tenant context errors"""
    pass


def get_current_tenant_id(request: Request) -> str:
    """
    Dependency to get tenant_id from the request.
    
    Raises:
        HTTPException: If tenant_id is not available
    """
    if not hasattr(request.state, 'tenant_id'):
        raise HTTPException(
            status_code=500,
            detail="Tenant context not available. Ensure APIKeyMiddleware is enabled."
        )
    return request.state.tenant_id


def get_current_tenant_name(request: Request) -> str:
    """
    Dependency to get tenant_name from the request.
    """
    if not hasattr(request.state, 'tenant_name'):
        raise HTTPException(
            status_code=500,
            detail="Tenant context not available. Ensure APIKeyMiddleware is enabled."
        )
    return request.state.tenant_name


def get_tenant_context(request: Request) -> dict:
    """
    Dependency to get the full tenant context.

    Returns:
        dict: {
            "tenant_id": str,
            "tenant_name": str,
            "api_key_prefix": str
        }
    """
    if not hasattr(request.state, 'tenant_id'):
        raise HTTPException(
            status_code=500,
            detail="Tenant context not available. Ensure APIKeyMiddleware is enabled."
        )

    return {
        "tenant_id": request.state.tenant_id,
        "tenant_name": request.state.tenant_name,
        "api_key_prefix": getattr(request.state, 'api_key_prefix', 'unknown')
    }
