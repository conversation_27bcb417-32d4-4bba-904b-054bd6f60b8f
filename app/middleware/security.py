"""
Security Middlewares for HTTP Headers and Timing Attack Protection

This module provides FastAPI middlewares to enhance application security by
adding various HTTP security headers and implementing timing attack protection.

Functions:
    get_csp_for_path: Generates a Content Security Policy (CSP) based on the request path and environment.

Classes:
    SecurityHeadersMiddleware: Adds a set of standard security headers to all HTTP responses.
    TimingAttackProtectionMiddleware: Introduces a minimum response time for sensitive endpoints
                                      to mitigate timing attacks.

Key Features:
    - Configurable Content Security Policy (CSP) to prevent XSS and data injection attacks.
    - X-Content-Type-Options, X-Frame-Options, X-XSS-Protection, Referrer-Policy, and Permissions-Policy headers.
    - HTTP Strict Transport Security (HSTS) for secure connections.
    - Server header removal for security through obscurity.
    - Consistent response timing for authentication-related endpoints.
"""
import asyncio
import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
from app.core.config import settings


def get_csp_for_path(path: str, environment: str) -> str:
    """
    Generate Content Security Policy based on path and environment

    Args:
        path: Request path
        environment: Current environment (development, staging, production)

    Returns:
        CSP header string
    """
    if path in ["/docs", "/redoc", "/openapi.json"]:
        return (
            "default-src 'self'; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https://fastapi.tiangolo.com; "
            "font-src 'self' https://cdn.jsdelivr.net; "
            "connect-src 'self';"
        )

    if environment == "production":
        return (
            "default-src 'none'; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'none';"
        )

    return (
        "default-src 'self'; "
        "img-src 'self' data:; "
        "style-src 'self' 'unsafe-inline'; "
        "script-src 'self' 'unsafe-inline'; "
        "connect-src 'self';"
    )


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add security headers to all responses
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

        csp = get_csp_for_path(request.url.path, settings.ENVIRONMENT.value)
        response.headers["Content-Security-Policy"] = csp

        if "server" in response.headers:
            del response.headers["server"]

        return response


class TimingAttackProtectionMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add consistent timing to prevent timing attacks
    """
    
    def __init__(self, app, min_response_time: float = 0.1):
        super().__init__(app)
        self.min_response_time = min_response_time
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        response = await call_next(request)
        
        # Ensure minimum response time for auth endpoints
        if request.url.path.startswith("/api/v1/auth/"):
            elapsed_time = time.time() - start_time
            if elapsed_time < self.min_response_time:
                await asyncio.sleep(self.min_response_time - elapsed_time)
        
        return response



