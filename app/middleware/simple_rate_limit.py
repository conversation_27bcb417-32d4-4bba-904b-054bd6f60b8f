"""
Simple Rate Limiting Middleware

This module provides a basic in-memory rate limiting middleware for FastAPI.
It helps protect endpoints from excessive requests, which can be useful for
preventing brute-force attacks or resource exhaustion.

Classes:
    SimpleRateLimiter: An in-memory implementation of a rate limiter.
    SimpleRateLimitMiddleware: FastAPI middleware that applies rate limiting to configured endpoints.

Key Features:
    - Tracks requests by client IP and endpoint.
    - Configurable limits and time windows for different endpoints.
    - Returns a 429 Too Many Requests response when limits are exceeded.
    - Integrates with the application's logging for rate limit events.
"""
import time
from collections import defaultdict, deque
from typing import Dict
from fastapi import Request, HTTPException, Response
from starlette.middleware.base import BaseHTTPMiddleware
import logging

logger = logging.getLogger(__name__)


class SimpleRateLimiter:
    """Simple in-memory rate limiter"""
    
    def __init__(self):
        self.requests: Dict[str, deque] = defaultdict(deque)
    
    def is_allowed(self, key: str, limit: int, window_seconds: int) -> bool:
        """Check if request is allowed"""
        now = time.time()
        window_start = now - window_seconds
        
        # Clean old requests
        while self.requests[key] and self.requests[key][0] < window_start:
            self.requests[key].popleft()
        
        # Check limit
        if len(self.requests[key]) >= limit:
            return False
        
        # Add current request
        self.requests[key].append(now)
        return True


class SimpleRateLimitMiddleware(BaseHTTPMiddleware):
    """Simple rate limiting middleware"""
    
    def __init__(self, app):
        super().__init__(app)
        self.limiter = SimpleRateLimiter()
        
        self.rate_limits = {
            "/api/v1/auth/login": {"limit": 5, "window": 900},
        }
    
    def get_client_ip(self, request: Request) -> str:
        """Get client IP"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    async def dispatch(self, request: Request, call_next) -> Response:
        path = request.url.path
        if path in self.rate_limits:
            config = self.rate_limits[path]
            client_ip = self.get_client_ip(request)
            rate_limit_key = f"{client_ip}:{path}"
            
            # DEBUG level - only in development
            logger.debug(f"Rate limit check for {client_ip} on {path}")

            try:
                is_allowed = self.limiter.is_allowed(
                    rate_limit_key,
                    config["limit"],
                    config["window"]
                )

                if not is_allowed:
                    # WARNING level - important security event
                    logger.warning(
                        f"Rate limit exceeded for {client_ip} on {path}",
                        extra={
                            "extra_data": {
                                "event_type": "rate_limit_exceeded",
                                "client_ip": client_ip,
                                "endpoint": path,
                                "limit": config["limit"],
                                "window": config["window"]
                            }
                        }
                    )
                    from fastapi.responses import JSONResponse
                    return JSONResponse(
                        status_code=429,
                        content={"detail": "Too many requests"},
                        headers={"Retry-After": "60"}
                    )
                else:
                    # DEBUG level - only in development
                    logger.debug(f"Rate limit check passed for {client_ip} on {path}")

            except Exception as e:
                # ERROR level - system issue
                logger.error(f"Rate limiting system error: {e}", exc_info=True)
                # Continue on error - don't block legitimate requests
                pass
        
        # Process request
        response = await call_next(request)
        return response
