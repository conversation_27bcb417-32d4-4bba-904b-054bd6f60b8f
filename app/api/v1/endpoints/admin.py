from datetime import timed<PERSON><PERSON>, datetime, timezone
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2Password<PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from uuid import UUID

from app.api.v1.schemas import TenantAPIKeyCreate, TenantAPIKeyUpdate, TenantAdminResponse, Message, AdminUserLogin, AdminToken
from app.db.base import SessionLocal
from app.db.models import Tenant, AdminUser
from app.core import security
from app.core.logging import get_security_logger
from app.core.config import settings

router = APIRouter()

security_logger = get_security_logger()

# Dependency to get DB session
async def get_db():
    async with SessionLocal() as session:
        yield session

# OAuth2PasswordBearer for admin token
reusable_oauth2_admin = OAuth2PasswordBearer(
    tokenUrl="/api/v1/admin/login"
)

async def get_current_admin_user(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(reusable_oauth2_admin)
) -> AdminUser:
    try:
        payload = security.jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        email: str = payload.get("sub")
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Could not validate credentials",
            )
        result = await db.execute(select(AdminUser).filter(AdminUser.email == email))
        admin_user = result.scalar_one_or_none()
        if admin_user is None:
            raise HTTPException(status_code=404, detail="Admin user not found")
        if not admin_user.is_active:
            raise HTTPException(status_code=400, detail="Inactive admin user")
        return admin_user
    except security.JWTError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )

@router.post("/login", response_model=AdminToken, status_code=status.HTTP_200_OK)
async def admin_login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate admin user and return JWT token.
    """
    result = await db.execute(select(AdminUser).filter(AdminUser.email == form_data.username))
    admin_user = result.scalar_one_or_none()

    if not admin_user or not security.verify_password(
        form_data.password, admin_user.hashed_password
    ):
        security_logger.warning(
            "Admin login failed: Invalid credentials",
            extra={"component": "admin_auth", "email": form_data.username}
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect email or password"
        )

    if not admin_user.is_active:
        security_logger.warning(
            "Admin login failed: Inactive user",
            extra={"component": "admin_auth", "email": form_data.username}
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive admin user")

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        subject=admin_user.email,
        expires_delta=access_token_expires
    )

    security_logger.info(
        "Admin login successful",
        extra={"component": "admin_auth", "email": admin_user.email}
    )

    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/tenants/{tenant_id}/api-key", response_model=Message, status_code=status.HTTP_200_OK)
async def create_tenant_api_key(
    tenant_id: UUID,
    api_key_in: TenantAPIKeyCreate,
    db: AsyncSession = Depends(get_db),
    current_admin_user: AdminUser = Depends(get_current_admin_user)
):
    """
    Generates and associates a new API Key hash with a tenant.
    """
    result = await db.execute(select(Tenant).filter(Tenant.id == tenant_id))
    tenant = result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )

    hashed_api_key = security.get_password_hash(api_key_in.api_key)
    tenant.api_key_hash = hashed_api_key
    db.add(tenant)
    await db.commit()
    await db.refresh(tenant)

    security_logger.info(
        f"API Key created/updated for tenant {tenant_id}",
        extra={"component": "admin_api_key", "tenant_id": str(tenant_id), "admin_email": current_admin_user.email}
    )
    return {"message": f"API Key for tenant {tenant_id} created/updated successfully"}

@router.put("/tenants/{tenant_id}/api-key", response_model=Message, status_code=status.HTTP_200_OK)
async def update_tenant_api_key(
    tenant_id: UUID,
    api_key_in: TenantAPIKeyUpdate,
    db: AsyncSession = Depends(get_db),
    current_admin_user: AdminUser = Depends(get_current_admin_user)
):
    """
    Updates the API Key hash for an existing tenant.
    """
    result = await db.execute(select(Tenant).filter(Tenant.id == tenant_id))
    tenant = result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )

    hashed_api_key = security.get_password_hash(api_key_in.api_key)
    tenant.api_key_hash = hashed_api_key
    db.add(tenant)
    await db.commit()
    await db.refresh(tenant)

    security_logger.info(
        f"API Key updated for tenant {tenant_id}",
        extra={"component": "admin_api_key", "tenant_id": str(tenant_id), "admin_email": current_admin_user.email}
    )
    return {"message": f"API Key for tenant {tenant_id} updated successfully"}

@router.get("/tenants", response_model=List[TenantAdminResponse], status_code=status.HTTP_200_OK)
async def list_tenants(
    db: AsyncSession = Depends(get_db),
    current_admin_user: AdminUser = Depends(get_current_admin_user)
):
    """
    Lists all tenants with their API Key status (presence of hash).
    Does NOT expose the API Key itself.
    """
    result = await db.execute(select(Tenant))
    tenants = result.scalars().all()
    return [
        TenantAdminResponse(
            id=tenant.id,
            name=tenant.name,
            description=tenant.description,
            is_active=tenant.is_active,
            created_at=tenant.created_at,
            updated_at=tenant.updated_at,
            has_api_key=tenant.api_key_hash is not None
        )
        for tenant in tenants
    ]

@router.delete("/tenants/{tenant_id}/api-key", response_model=Message, status_code=status.HTTP_200_OK)
async def delete_tenant_api_key(
    tenant_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_admin_user: AdminUser = Depends(get_current_admin_user)
):
    """
    Removes the API Key hash from a tenant, effectively revoking its API access.
    """
    result = await db.execute(select(Tenant).filter(Tenant.id == tenant_id))
    tenant = result.scalar_one_or_none()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )

    tenant.api_key_hash = None
    db.add(tenant)
    await db.commit()
    await db.refresh(tenant)

    security_logger.info(
        f"API Key revoked for tenant {tenant_id}",
        extra={"component": "admin_api_key", "tenant_id": str(tenant_id), "admin_email": current_admin_user.email}
    )
    return {"message": f"API Key for tenant {tenant_id} revoked successfully"}
