"""
Pydantic Schemas for API Data Validation and Serialization

This module defines the data models (schemas) used for request and response
validation in the API. These schemas ensure data consistency and provide clear
documentation for the API's data structures.

Classes:
    UserCreate: Schema for creating a new user.
    User: Schema for representing a user in API responses.
    Token: Schema for JWT token responses.
    TokenData: Schema for data contained within a JWT token.
    PasswordResetRequest: Schema for requesting a password reset.
    PasswordResetConfirm: Schema for confirming a password reset with a new password.
    PasswordChange: Schema for changing an existing password.
    Message: Generic schema for simple API messages.

Key Features:
    - Data validation using Pydantic.
    - Clear definition of API input and output formats.
    - Integration with FastAPI's automatic documentation.
"""
from pydantic import BaseModel, Field, EmailStr, ConfigDict
from uuid import UUID
from datetime import datetime
from typing import Optional


class UserCreate(BaseModel):
    email: EmailStr
    password: str


class User(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    tenant_id: UUID
    email: str
    is_active: bool
    created_at: datetime
    updated_at: datetime


class Token(BaseModel):
    """
    JWT Token response with tenant information.
    Note: For login, use email in the 'username' field of OAuth2PasswordRequestForm.
    """
    access_token: str
    token_type: str
    tenant_id: UUID
    tenant_name: str


class TokenData(BaseModel):
    username: str | None = None


class PasswordResetRequest(BaseModel):
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8)


class PasswordChange(BaseModel):
    old_password: str
    new_password: str = Field(..., min_length=8)


class Message(BaseModel):
    message: str


class TenantAPIKeyCreate(BaseModel):
    tenant_id: UUID
    api_key: str = Field(..., min_length=32, description="The plain API key to be hashed and stored.")


class TenantAPIKeyUpdate(BaseModel):
    tenant_id: UUID
    api_key: str = Field(..., min_length=32, description="The new plain API key to be hashed and stored.")


class TenantAdminResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    name: str
    description: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime
    has_api_key: bool


class AdminUserCreate(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=8)


class AdminUserLogin(BaseModel):
    email: EmailStr
    password: str


class AdminToken(BaseModel):
    access_token: str
    token_type: str


class AdminStatsResponse(BaseModel):
    """
    Response schema for /admin/stats endpoint as per QUANTHEACORP.md specification.
    """
    tenant_id: str
    total_users: int
    active_users_last_30d: int
    database_size_mb: float
    avg_response_time_ms: float
    error_rate_percent: float


class AdminLogEntry(BaseModel):
    """
    Individual log entry for /admin/logs endpoint.
    """
    timestamp: str  # ISO8601 format
    event: str
    details: dict


class AdminLogsResponse(BaseModel):
    """
    Response schema for /admin/logs endpoint as per QUANTHEACORP.md specification.
    """
    logs: list[AdminLogEntry]
