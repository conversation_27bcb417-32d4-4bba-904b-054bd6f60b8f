FROM python:3.13-slim

# Create a non-root user and group
ARG UID=1000
ARG GID=1000
RUN groupadd -g $GID appgroup && useradd -u $UID -g appgroup -m appuser

WORKDIR /app

ENV PYTHONPATH=/app

# Install PostgreSQL client tools
RUN apt-get update && apt-get install -y --no-install-recommends curl gnupg2 && curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor | tee /etc/apt/trusted.gpg.d/apt.postgresql.org.gpg >/dev/null && echo "deb http://apt.postgresql.org/pub/repos/apt/ bookworm-pgdg main" | tee /etc/apt/sources.list.d/pgdg.list && apt-get update && apt-get install -y --no-install-recommends postgresql-client-16 && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# Set ownership of /app to appuser
RUN chown -R appuser:appgroup /app

# Copy and set entrypoint script
COPY entrypoint.sh .
RUN chmod +x entrypoint.sh

# Switch to the non-root user
USER appuser

# Use entrypoint for migrations and startup
ENTRYPOINT ["./entrypoint.sh"]
